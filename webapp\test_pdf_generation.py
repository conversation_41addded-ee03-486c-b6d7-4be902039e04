#!/usr/bin/env python3
"""
Test script per verificare la generazione PDF delle certificazioni
"""

import requests
import json
import sys
import os

# Configurazione
BASE_URL = "http://localhost:8001"
CANTIERE_ID = 1  # Assumiamo che esista un cantiere con ID 1

def test_api_connection():
    """Test della connessione API"""
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        if response.status_code == 200:
            print("✅ API connessa e funzionante")
            print(f"Risposta: {response.json()}")
            return True
        else:
            print(f"❌ Errore nella connessione API: {response.status_code}")
            print(f"Dettagli: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Errore di connessione all'API: {e}")
        return False

def get_certificazioni():
    """Recupera le certificazioni disponibili"""
    try:
        response = requests.get(f"{BASE_URL}/api/cantieri/{CANTIERE_ID}/certificazioni")
        if response.status_code == 200:
            certificazioni = response.json()
            print(f"✅ Trovate {len(certificazioni)} certificazioni")
            return certificazioni
        else:
            print(f"❌ Errore nel recupero certificazioni: {response.status_code}")
            print(f"Dettagli: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Errore di connessione: {e}")
        return []

def test_pdf_generation(certificazione_id):
    """Test della generazione PDF per una certificazione specifica"""
    try:
        print(f"🔄 Test generazione PDF per certificazione {certificazione_id}...")
        
        response = requests.get(
            f"{BASE_URL}/api/cantieri/{CANTIERE_ID}/certificazioni/{certificazione_id}/pdf",
            stream=True
        )
        
        if response.status_code == 200:
            # Salva il PDF per verificare
            filename = f"test_certificato_{certificazione_id}.pdf"
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            file_size = os.path.getsize(filename)
            print(f"✅ PDF generato con successo: {filename} ({file_size} bytes)")
            
            # Rimuovi il file di test
            os.remove(filename)
            return True
        else:
            print(f"❌ Errore nella generazione PDF: {response.status_code}")
            print(f"Dettagli: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Errore nella generazione PDF: {e}")
        return False

def main():
    """Funzione principale di test"""
    print("🚀 Test del sistema di certificazione cavi")
    print("=" * 50)
    
    # Test 1: Verifica connessione API
    print("\n1. Test connessione API...")
    if not test_api_connection():
        print("❌ L'API non è disponibile. Verifica che il backend sia avviato.")
        sys.exit(1)
    
    # Test 2: Recupera certificazioni
    print("\n2. Recupero certificazioni...")
    certificazioni = get_certificazioni()
    
    if not certificazioni:
        print("⚠️  Nessuna certificazione trovata. Crea almeno una certificazione per testare la generazione PDF.")
        return
    
    # Test 3: Genera PDF per la prima certificazione
    print("\n3. Test generazione PDF...")
    prima_certificazione = certificazioni[0]
    certificazione_id = prima_certificazione.get('id_certificazione')
    
    if certificazione_id:
        success = test_pdf_generation(certificazione_id)
        if success:
            print("\n✅ Tutti i test completati con successo!")
        else:
            print("\n❌ Test fallito nella generazione PDF")
    else:
        print("❌ ID certificazione non trovato")

if __name__ == "__main__":
    main()
