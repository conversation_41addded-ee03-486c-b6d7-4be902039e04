#!/usr/bin/env python3
"""
Script per avviare il backend con il corretto PYTHONPATH
"""

import os
import sys
import subprocess

def main():
    # Aggiungi la directory webapp al PYTHONPATH
    webapp_dir = os.path.dirname(os.path.abspath(__file__))
    if webapp_dir not in sys.path:
        sys.path.insert(0, webapp_dir)
    
    # Cambia directory al backend
    backend_dir = os.path.join(webapp_dir, 'backend')
    os.chdir(backend_dir)
    
    # Avvia uvicorn
    try:
        import uvicorn
        print("Avvio del backend FastAPI...")
        print(f"Directory: {backend_dir}")
        print(f"PYTHONPATH: {webapp_dir}")
        
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8001,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\nServer fermato dall'utente")
    except Exception as e:
        print(f"Errore nell'avvio del server: {e}")

if __name__ == "__main__":
    main()
