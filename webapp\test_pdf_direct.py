#!/usr/bin/env python3
"""
Test diretto della generazione PDF senza autenticazione
"""

import sys
import os
sys.path.insert(0, '.')

from backend.database import get_db
from backend.models.certificazione_cavo import CertificazioneCavo
from backend.services.pdf_cei_64_8_service import PDFCEI64_8Service

def test_pdf_generation_direct():
    """Test diretto della generazione PDF"""
    try:
        print("🔄 Test generazione PDF diretto...")
        
        # Ottieni una sessione del database
        db = next(get_db())
        
        # Trova una certificazione
        cert = db.query(CertificazioneCavo).first()
        if not cert:
            print("❌ Nessuna certificazione trovata nel database")
            return False
        
        print(f"✅ Certificazione trovata: ID {cert.id_certificazione}, Cavo {cert.id_cavo}")
        
        # Crea il servizio PDF
        pdf_service = PDFCEI64_8Service(db)
        
        # Genera il PDF
        pdf_path = pdf_service.generate_certificato_singolo_pdf(cert.id_certificazione)
        
        # Verifica che il file sia stato creato
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"✅ PDF generato con successo: {pdf_path} ({file_size} bytes)")
            
            # Rimuovi il file di test
            os.remove(pdf_path)
            print("✅ File di test rimosso")
            return True
        else:
            print(f"❌ File PDF non trovato: {pdf_path}")
            return False
            
    except Exception as e:
        print(f"❌ Errore nella generazione PDF: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Funzione principale di test"""
    print("🚀 Test diretto del sistema di generazione PDF")
    print("=" * 50)
    
    success = test_pdf_generation_direct()
    
    if success:
        print("\n✅ Test completato con successo!")
        print("Il sistema di generazione PDF funziona correttamente.")
    else:
        print("\n❌ Test fallito!")
        print("Ci sono problemi con la generazione PDF.")

if __name__ == "__main__":
    main()
