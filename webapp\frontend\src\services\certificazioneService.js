import config from '../config';
import axiosInstance from './axiosConfig';

const certificazioneService = {
  // Ottiene la lista delle certificazioni di un cantiere
  getCertificazioni: async (cantiereId, filtroCavo = '') => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      let url = `/cantieri/${cantiereIdNum}/certificazioni`;
      if (filtroCavo) {
        url += `?filtro_cavo=${filtroCavo}`;
      }
      const response = await axiosInstance.get(url);
      return response.data;
    } catch (error) {
      console.error('Get certificazioni error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea una nuova certificazione
  createCertificazione: async (cantiereId, certificazioneData) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni`, certificazioneData);
      return response.data;
    } catch (error) {
      console.error('Create certificazione error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene i dettagli di una certificazione
  getCertificazione: async (cantiereId, idCertificazione) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/${idCertificazione}`);
      return response.data;
    } catch (error) {
      console.error('Get certificazione error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna una certificazione
  updateCertificazione: async (cantiereId, idCertificazione, certificazioneData) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/certificazioni/${idCertificazione}`, certificazioneData);
      return response.data;
    } catch (error) {
      console.error('Update certificazione error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Elimina una certificazione
  deleteCertificazione: async (cantiereId, idCertificazione) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/certificazioni/${idCertificazione}`);
      return response.data;
    } catch (error) {
      console.error('Delete certificazione error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Genera PDF di una certificazione
  generatePdf: async (cantiereId, idCertificazione) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/${idCertificazione}/pdf`, {
        responseType: 'blob'
      });

      // Crea un URL per il blob e scarica il file
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);

      // Crea un link temporaneo per il download
      const link = document.createElement('a');
      link.href = url;
      link.download = `certificato_${idCertificazione}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Pulisci l'URL
      window.URL.revokeObjectURL(url);

      return { success: true, message: 'PDF scaricato con successo' };
    } catch (error) {
      console.error('Generate PDF error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene la lista degli strumenti certificati
  getStrumenti: async (cantiereId) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/strumenti`);
      return response.data;
    } catch (error) {
      console.error('Get strumenti error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Crea un nuovo strumento certificato
  createStrumento: async (cantiereId, strumentoData) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/strumenti`, strumentoData);
      return response.data;
    } catch (error) {
      console.error('Create strumento error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Aggiorna uno strumento certificato
  updateStrumento: async (cantiereId, idStrumento, strumentoData) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/strumenti/${idStrumento}`, strumentoData);
      return response.data;
    } catch (error) {
      console.error('Update strumento error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Elimina uno strumento certificato
  deleteStrumento: async (cantiereId, idStrumento) => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/strumenti/${idStrumento}`);
      return response.data;
    } catch (error) {
      console.error('Delete strumento error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Funzioni avanzate per il nuovo sistema

  // Ottiene statistiche delle certificazioni
  getStatistiche: async (cantiereId) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/statistiche`);
      return response.data;
    } catch (error) {
      console.error('Get statistiche error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Esporta certificazioni in formato CSV
  exportCertificazioni: async (cantiereId, filtri = {}) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const params = new URLSearchParams(filtri);
      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/export?${params}`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Export certificazioni error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Genera report completo delle certificazioni
  generateReport: async (cantiereId, tipoReport = 'completo') => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/certificazioni/report/${tipoReport}`);
      return response.data;
    } catch (error) {
      console.error('Generate report error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Operazioni bulk per certificazioni
  bulkDelete: async (cantiereId, idCertificazioni) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni/bulk-delete`, {
        ids: idCertificazioni
      });
      return response.data;
    } catch (error) {
      console.error('Bulk delete error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Genera PDF multipli
  generateBulkPdf: async (cantiereId, idCertificazioni) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni/bulk-pdf`, {
        ids: idCertificazioni
      }, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Bulk PDF error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Valida dati certificazione
  validateCertificazione: async (cantiereId, certificazioneData) => {
    try {
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/certificazioni/validate`, certificazioneData);
      return response.data;
    } catch (error) {
      console.error('Validate certificazione error:', error);
      throw error.response ? error.response.data : error;
    }
  }
};

export default certificazioneService;
