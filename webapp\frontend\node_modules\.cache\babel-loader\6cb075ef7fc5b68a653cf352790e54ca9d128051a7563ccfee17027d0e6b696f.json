{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport { Box, Typography, Paper, Grid, Card, CardContent, Button, Chip, Alert, CircularProgress, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails, Switch, FormControlLabel } from '@mui/material';\nimport { Assessment as AssessmentIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, DateRange as DateRangeIcon, Cable as CableIcon, ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon, ShowChart as ShowChartIcon } from '@mui/icons-material';\nimport { useParams } from 'react-router-dom';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const {\n    cantiereId\n  } = useParams();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobinaSpecifica: null,\n    storicoBobine: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // State per storico bobine accordion\n  const [expandedBobine, setExpandedBobine] = useState(new Set());\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Import certificazione service\n        const certificazioneService = await import('../../services/certificazioneService');\n\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading progress report:', err);\n          return {\n            content: null\n          };\n        });\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video').catch(err => {\n          console.error('Error loading BOQ report:', err);\n          return {\n            content: null\n          };\n        });\n\n        // Carica statistiche certificazioni\n        const certificazioniPromise = certificazioneService.default.getCertificazioni(cantiereId).catch(err => {\n          console.error('Error loading certificazioni:', err);\n          return [];\n        });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, certificazioniData] = await Promise.all([progressPromise, boqPromise, certificazioniPromise]);\n\n        // Aggiungi statistiche certificazioni ai dati del progress report\n        if (progressData.content && certificazioniData) {\n          const totaleCavi = progressData.content.totale_cavi || 0;\n          const caviCertificati = certificazioniData.length;\n          const percentualeCertificazione = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n\n          // Calcola certificazioni di oggi\n          const oggi = new Date().toDateString();\n          const certificazioniOggi = certificazioniData.filter(cert => new Date(cert.data_certificazione).toDateString() === oggi).length;\n          progressData.content.certificazioni = {\n            totale: caviCertificati,\n            percentuale: percentualeCertificazione,\n            oggi: certificazioniOggi,\n            rimanenti: totaleCavi - caviCertificati\n          };\n        }\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobinaSpecifica: null,\n          storicoBobine: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Load storico bobine when selected\n  useEffect(() => {\n    if (cantiereId && selectedReportType === 'storico-bobine') {\n      loadStoricoBobine();\n    }\n  }, [cantiereId, selectedReportType]);\n\n  // Function to load storico bobine data\n  const loadStoricoBobine = async () => {\n    try {\n      setLoading(true);\n      // Chiamata API reale per ottenere i dati dello storico bobine\n      const response = await fetch(`/api/reports/${cantiereId}/storico-bobine?formato=video`);\n      if (!response.ok) {\n        throw new Error(`Errore HTTP: ${response.status}`);\n      }\n      const result = await response.json();\n      if (result.success && result.content) {\n        setReportsData(prev => ({\n          ...prev,\n          storicoBobine: result.content\n        }));\n      } else {\n        throw new Error(result.message || 'Errore nel caricamento dei dati');\n      }\n    } catch (err) {\n      console.error('Error loading storico bobine:', err);\n      setError('Errore nel caricamento dello storico bobine: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 1,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCA Report Avanzamento Lavori\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Totali\",\n          value: data.metri_totali,\n          unit: \"m\",\n          subtitle: \"Lunghezza complessiva del progetto\",\n          gradient: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Posati\",\n          value: data.metri_posati,\n          unit: \"m\",\n          subtitle: `${data.percentuale_avanzamento}% completato`,\n          gradient: \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n          progress: data.percentuale_avanzamento,\n          trend: data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down',\n          trendValue: `${data.percentuale_avanzamento}%`,\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Rimanenti\",\n          value: data.metri_da_posare,\n          unit: \"m\",\n          subtitle: `${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`,\n          gradient: \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Media/Giorno\",\n          value: data.media_giornaliera || 0,\n          unit: \"m\",\n          subtitle: data.giorni_stimati ? `${data.giorni_stimati} giorni lavorativi rimasti` : data.media_giornaliera > 0 ? 'Calcolo in corso' : 'Nessuna posa recente',\n          gradient: \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n          size: \"large\",\n          tooltip: data.giorni_lavorativi_effettivi ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.` : 'Media giornaliera basata sui giorni di lavoro effettivo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(ProgressChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      sx: {\n        mb: 5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                sx: {\n                  color: '#3498db',\n                  mr: 1,\n                  fontSize: 28\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"Stato Cavi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    bgcolor: '#f8f9fa',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#2c3e50',\n                      mb: 1\n                    },\n                    children: data.totale_cavi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Cavi Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    bgcolor: '#e8f5e8',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#27ae60',\n                      mb: 1\n                    },\n                    children: data.cavi_posati\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: [\"Cavi Posati (\", data.percentuale_cavi, \"%)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Progresso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: [data.percentuale_cavi, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  bgcolor: '#9c27b0',\n                  borderRadius: '50%',\n                  p: 1,\n                  mr: 2,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: \"\\uD83D\\uDD12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"Stato Certificazioni Cavi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this), data.certificazioni ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                sx: {\n                  mb: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center',\n                      p: 2,\n                      bgcolor: '#e8f5e8',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      sx: {\n                        fontWeight: 700,\n                        color: '#27ae60',\n                        mb: 1\n                      },\n                      children: data.certificazioni.totale\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 470,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: \"Certificati\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center',\n                      p: 2,\n                      bgcolor: '#fff3cd',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      sx: {\n                        fontWeight: 700,\n                        color: '#856404',\n                        mb: 1\n                      },\n                      children: data.certificazioni.rimanenti\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: \"Da Certificare\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 700,\n                    color: '#9c27b0',\n                    mb: 1\n                  },\n                  children: [data.certificazioni.percentuale, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    mb: 1\n                  },\n                  children: \"Completamento Certificazioni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#999',\n                    fontSize: '0.75rem'\n                  },\n                  children: [data.certificazioni.oggi, \" certificazioni completate oggi\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: `${data.certificazioni.percentuale}%`,\n                    height: '100%',\n                    bgcolor: '#9c27b0',\n                    transition: 'width 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                p: 2,\n                bgcolor: '#f8f9fa',\n                borderRadius: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Nessuna certificazione disponibile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(DateRangeIcon, {\n            sx: {\n              color: '#9b59b6',\n              mr: 1,\n              fontSize: 28\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              color: '#2c3e50'\n            },\n            children: \"\\uD83D\\uDCC8 Attivit\\xE0 Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: data.posa_recente.slice(0, 5).map((posa, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                border: '1px solid #e0e0e0',\n                borderRadius: 2,\n                bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                transition: 'all 0.2s',\n                '&:hover': {\n                  bgcolor: '#f5f5f5',\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666',\n                  mb: 1\n                },\n                children: posa.data\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50'\n                },\n                children: [posa.metri, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 21\n              }, this), index === 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Pi\\xF9 recente\",\n                size: \"small\",\n                sx: {\n                  mt: 1,\n                  bgcolor: '#3498db',\n                  color: 'white',\n                  fontSize: '0.7rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 13\n        }, this), data.posa_recente.length > 5 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Accordion, {\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 49\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#3498db'\n                },\n                children: [\"Mostra tutti i \", data.posa_recente.length, \" record\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n                data: data.posa_recente.map(posa => ({\n                  data: posa.data,\n                  metri: `${posa.metri}m`\n                })),\n                columns: [{\n                  field: 'data',\n                  headerName: 'Data',\n                  width: 200\n                }, {\n                  field: 'metri',\n                  headerName: 'Metri Posati',\n                  width: 150,\n                  align: 'right'\n                }],\n                pagination: true,\n                pageSize: 10\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 284,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 1,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n        sx: {\n          color: '#8e44ad',\n          mr: 1,\n          fontSize: 28\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCB Bill of Quantities - Distinta Materiali\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 618,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(BoqChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 635,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 616,\n    columnNumber: 5\n  }, this);\n  const renderStoricoBobineReport = data => {\n    const toggleBobina = bobinaId => {\n      const newExpanded = new Set(expandedBobine);\n      if (newExpanded.has(bobinaId)) {\n        newExpanded.delete(bobinaId);\n      } else {\n        newExpanded.add(bobinaId);\n      }\n      setExpandedBobine(newExpanded);\n    };\n    const getStatoColor = stato => {\n      switch (stato) {\n        case 'Disponibile':\n          return '#27ae60';\n        case 'In Uso':\n          return '#f39c12';\n        case 'Esaurita':\n          return '#e74c3c';\n        default:\n          return '#95a5a6';\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 3,\n          p: 2,\n          bgcolor: '#f8f9fa',\n          borderRadius: 1,\n          border: '1px solid #e0e0e0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n          sx: {\n            color: '#9b59b6',\n            mr: 1,\n            fontSize: 28\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            fontWeight: 600,\n            color: '#2c3e50'\n          },\n          children: \"\\uD83D\\uDCE6 Storico Bobine - Cavi Associati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: data.bobine.map(bobina => /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            mb: 2,\n            border: '1px solid #e0e0e0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 3,\n              cursor: 'pointer',\n              bgcolor: expandedBobine.has(bobina.id) ? '#f8f9fa' : 'white',\n              borderBottom: expandedBobine.has(bobina.id) ? '1px solid #e0e0e0' : 'none',\n              '&:hover': {\n                bgcolor: '#f5f5f5'\n              }\n            },\n            onClick: () => toggleBobina(bobina.id),\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 8,\n                      height: 8,\n                      borderRadius: '50%',\n                      bgcolor: getStatoColor(bobina.stato),\n                      mr: 2\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 600,\n                        color: '#2c3e50'\n                      },\n                      children: bobina.codice\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: bobina.tipologia\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    mb: 0.5\n                  },\n                  children: \"Formazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 500\n                  },\n                  children: bobina.formazione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    mb: 0.5\n                  },\n                  children: \"Metri Totali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 500\n                  },\n                  children: [bobina.metri_totali, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    mb: 0.5\n                  },\n                  children: \"Utilizzati\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 500,\n                    color: '#e74c3c'\n                  },\n                  children: [bobina.metri_utilizzati, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    mb: 0.5\n                  },\n                  children: \"Residui\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 500,\n                    color: '#27ae60'\n                  },\n                  children: [bobina.metri_residui, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 1,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'center'\n                  },\n                  children: expandedBobine.has(bobina.id) ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {\n                    sx: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 758,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {\n                    sx: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 759,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 15\n          }, this), expandedBobine.has(bobina.id) && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 3,\n              bgcolor: '#fafafa'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2,\n                fontWeight: 600,\n                color: '#2c3e50'\n              },\n              children: [\"Cavi Associati (\", bobina.cavi_associati.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 769,\n              columnNumber: 19\n            }, this), bobina.cavi_associati.length > 0 ? /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: bobina.cavi_associati.map(cavo => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 4,\n                children: /*#__PURE__*/_jsxDEV(Paper, {\n                  sx: {\n                    p: 2,\n                    border: '1px solid #e0e0e0',\n                    bgcolor: 'white'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    sx: {\n                      fontWeight: 600,\n                      mb: 1\n                    },\n                    children: cavo.nomenclatura\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666',\n                      mb: 0.5\n                    },\n                    children: [\"Metri utilizzati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [cavo.metri_utilizzati, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 782,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: [\"Data posa: \", new Date(cavo.data_posa).toLocaleDateString('it-IT')]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 784,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 27\n                }, this)\n              }, cavo.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                p: 3,\n                bgcolor: 'white',\n                borderRadius: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Nessun cavo associato a questa bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 3,\n                p: 2,\n                bgcolor: 'white',\n                borderRadius: 1,\n                border: '1px solid #e0e0e0'\n              },\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 4,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666',\n                      mb: 0.5\n                    },\n                    children: \"Fornitore\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 803,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: bobina.fornitore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 806,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 4,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666',\n                      mb: 0.5\n                    },\n                    children: \"Data Arrivo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: new Date(bobina.data_arrivo).toLocaleDateString('it-IT')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 814,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 4,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666',\n                      mb: 0.5\n                    },\n                    children: \"Stato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 819,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        bgcolor: getStatoColor(bobina.stato),\n                        mr: 1\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 823,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: bobina.stato\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 830,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 822,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 818,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 800,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 768,\n            columnNumber: 17\n          }, this)]\n        }, bobina.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 682,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          bgcolor: '#f8f9fa',\n          border: '1px solid #e0e0e0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2,\n            fontWeight: 600,\n            color: '#2c3e50'\n          },\n          children: \"\\uD83D\\uDCCA Riepilogo Generale\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 845,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50',\n                  mb: 1\n                },\n                children: data.bobine.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 851,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Bobine Totali\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 849,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50',\n                  mb: 1\n                },\n                children: data.bobine.reduce((sum, b) => sum + b.cavi_associati.length, 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Cavi Associati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50',\n                  mb: 1\n                },\n                children: [data.bobine.reduce((sum, b) => sum + b.metri_utilizzati, 0), \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 871,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Metri Utilizzati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 870,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 869,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50',\n                  mb: 1\n                },\n                children: [data.bobine.reduce((sum, b) => sum + b.metri_residui, 0), \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 881,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Metri Residui\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 848,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 7\n    }, this);\n  };\n  const renderPosaPeriodoReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'warning.main'\n        },\n        children: \"Report Posa per Periodo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 903,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 916,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 915,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 906,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 902,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      sx: {\n        mb: 5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'warning.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.totale_metri_periodo, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Metri Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: [data.data_inizio, \" - \", data.data_fine]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 931,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 925,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'info.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: data.giorni_attivi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Giorni Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 935,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 934,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'success.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.media_giornaliera, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 944,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Giorno\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 947,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'primary.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [Math.round(data.totale_metri_periodo / data.giorni_attivi * 7), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 952,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Settimana\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 955,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 951,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 950,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 924,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 5,\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(TimelineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 963,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 962,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Dettaglio Posa Giornaliera\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 969,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.posa_giornaliera || [],\n        columns: [{\n          field: 'data',\n          headerName: 'Data',\n          width: 200\n        }, {\n          field: 'metri',\n          headerName: 'Metri Posati',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri}m`\n        }],\n        pageSize: 10\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 972,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 968,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 900,\n    columnNumber: 5\n  }, this);\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: dialogType === 'posa-periodo' ? 'Report Posa per Periodo' : 'Genera Report'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 989,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 994,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1008,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1010,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1001,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 11\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1020,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1019,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1030,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1029,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 999,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 992,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1049,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1049,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1045,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1043,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 988,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"report-main-container report-fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1061,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1060,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1067,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1066,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600,\n            color: '#2c3e50',\n            mb: 2,\n            textAlign: 'center'\n          },\n          children: \"\\uD83C\\uDFAF Seleziona il tipo di report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1075,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: `report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`,\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('progress'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#3498db',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1093,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Avanzamento\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1094,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Panoramica lavori\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1097,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1092,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1081,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1080,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('boq'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#8e44ad',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1117,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Bill of Quantities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1118,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Distinta materiali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1121,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1116,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1106,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'storico-bobine' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'storico-bobine' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('storico-bobine'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#9b59b6',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1143,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Storico Bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Cavi per bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1132,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1078,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1074,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: '400px',\n          width: '100%'\n        },\n        children: [selectedReportType === 'progress' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            borderRadius: 2\n          },\n          children: reportsData.progress ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1165,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1164,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1175,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1174,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1163,\n              columnNumber: 19\n            }, this), renderProgressReport(reportsData.progress)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1162,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"progress\",\n            title: \"Caricamento Report Avanzamento...\",\n            description: \"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1187,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"progress\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getProgressReport(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  progress: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying progress report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1194,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1160,\n          columnNumber: 13\n        }, this), selectedReportType === 'boq' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            borderRadius: 2\n          },\n          children: reportsData.boq ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1228,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1227,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1238,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1237,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1226,\n              columnNumber: 19\n            }, this), renderBoqReport(reportsData.boq)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1225,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"boq\",\n            title: \"Caricamento Bill of Quantities...\",\n            description: \"Stiamo elaborando la distinta materiali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1250,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"boq\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getBillOfQuantities(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  boq: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying BOQ report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1257,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1223,\n          columnNumber: 13\n        }, this), selectedReportType === 'storico-bobine' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            borderRadius: 2\n          },\n          children: reportsData.storicoBobine ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1291,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('storico-bobine', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1290,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1301,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('storico-bobine', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1300,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1289,\n              columnNumber: 19\n            }, this), renderStoricoBobineReport(reportsData.storicoBobine)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1288,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"storico-bobine\",\n            title: \"Caricamento Storico Bobine...\",\n            description: \"Stiamo elaborando i dati delle bobine e dei cavi associati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1313,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"storico-bobine\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare lo storico bobine. Verifica la connessione e riprova.\",\n            onRetry: () => loadStoricoBobine(),\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1320,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1286,\n          columnNumber: 13\n        }, this), selectedReportType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.posaPeriodo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1339,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1338,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1349,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1348,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1337,\n              columnNumber: 19\n            }, this), renderPosaPeriodoReport(reportsData.posaPeriodo)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1336,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"action-required\",\n            reportType: \"posa-periodo\",\n            title: \"Seleziona un Periodo\",\n            description: \"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttivit\\xE0 del team.\",\n            actionLabel: \"Seleziona Periodo\",\n            onAction: () => {\n              setDialogType('posa-periodo');\n              // Set default date range (last month to today)\n              const today = new Date();\n              const lastMonth = new Date();\n              lastMonth.setMonth(today.getMonth() - 1);\n              setFormData({\n                ...formData,\n                data_inizio: lastMonth.toISOString().split('T')[0],\n                data_fine: today.toISOString().split('T')[0]\n              });\n              setOpenDialog(true);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1361,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1334,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1072,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1058,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"eQ0UGmsvcrJBK75aVpbKNwjrHh4=\", false, function () {\n  return [useParams];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "ExpandMore", "ExpandMoreIcon", "ExpandLess", "ExpandLessIcon", "ShowChart", "ShowChartIcon", "useParams", "AdminHomeButton", "reportService", "FilterableTable", "EmptyState", "MetricCard", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "cantiereId", "loading", "setLoading", "error", "setError", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedReportType", "setSelectedReportType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobinaSpecifica", "storicoBobine", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expandedBobine", "setExpandedBobine", "Set", "loadAllReports", "certificazioneService", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "certificazioniPromise", "default", "getCertificazioni", "progressData", "boqData", "certificazioniData", "Promise", "all", "totaleCavi", "totale_cavi", "caviCertificati", "length", "percentualeCertificazione", "Math", "round", "oggi", "Date", "toDateString", "certificazioniOggi", "filter", "cert", "data_certificazione", "certificazioni", "totale", "percentuale", "<PERSON><PERSON><PERSON>", "loadStoricoBobine", "response", "fetch", "ok", "Error", "status", "result", "json", "success", "prev", "message", "generateReportWithFormat", "reportType", "format", "getPosaPerPeriodoReport", "file_url", "window", "open", "detail", "handleGenerateReport", "handleCloseDialog", "renderProgressReport", "data", "children", "sx", "display", "justifyContent", "alignItems", "mb", "p", "bgcolor", "borderRadius", "border", "variant", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "control", "checked", "onChange", "e", "target", "label", "mr", "container", "spacing", "item", "xs", "sm", "md", "title", "value", "metri_totali", "unit", "subtitle", "gradient", "size", "metri_posati", "percentuale_avanzamento", "trend", "trendValue", "metri_da_posare", "toFixed", "media_giornaliera", "giorni_stimati", "tooltip", "giorni_lavorativi_effettivi", "height", "fontSize", "textAlign", "cavi_posati", "percentuale_cavi", "mt", "width", "overflow", "transition", "posa_recente", "slice", "map", "posa", "index", "transform", "boxShadow", "metri", "expandIcon", "columns", "field", "headerName", "align", "pagination", "pageSize", "renderBoqReport", "renderStoricoBobineReport", "to<PERSON><PERSON><PERSON><PERSON>", "bobina<PERSON>d", "newExpanded", "has", "delete", "add", "getStatoColor", "stato", "bobine", "bobina", "cursor", "id", "borderBottom", "onClick", "codice", "tipologia", "formazione", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "metri_residui", "cavi_associati", "cavo", "nomenclatura", "data_posa", "toLocaleDateString", "fornitore", "data_arrivo", "reduce", "sum", "b", "renderPosaPeriodoReport", "totale_metri_periodo", "giorni_attivi", "posa_giornal<PERSON>", "dataType", "renderCell", "row", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "type", "InputLabelProps", "shrink", "disabled", "startIcon", "className", "my", "flexDirection", "minHeight", "description", "onRetry", "then", "finally", "posaPeriodo", "actionLabel", "onAction", "today", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n\n\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n\n  ExpandMore as ExpandMoreIcon,\n  ExpandLess as ExpandLessIcon,\n  ShowChart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useParams } from 'react-router-dom';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\n\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\n\nconst ReportCaviPageNew = () => {\n  const { cantiereId } = useParams();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobinaSpecifica: null,\n    storicoBobine: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // State per storico bobine accordion\n  const [expandedBobine, setExpandedBobine] = useState(new Set());\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Import certificazione service\n        const certificazioneService = await import('../../services/certificazioneService');\n\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        // Carica statistiche certificazioni\n        const certificazioniPromise = certificazioneService.default.getCertificazioni(cantiereId)\n          .catch(err => {\n            console.error('Error loading certificazioni:', err);\n            return [];\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, certificazioniData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          certificazioniPromise\n        ]);\n\n        // Aggiungi statistiche certificazioni ai dati del progress report\n        if (progressData.content && certificazioniData) {\n          const totaleCavi = progressData.content.totale_cavi || 0;\n          const caviCertificati = certificazioniData.length;\n          const percentualeCertificazione = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n          // Calcola certificazioni di oggi\n          const oggi = new Date().toDateString();\n          const certificazioniOggi = certificazioniData.filter(cert =>\n            new Date(cert.data_certificazione).toDateString() === oggi\n          ).length;\n\n          progressData.content.certificazioni = {\n            totale: caviCertificati,\n            percentuale: percentualeCertificazione,\n            oggi: certificazioniOggi,\n            rimanenti: totaleCavi - caviCertificati\n          };\n        }\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobinaSpecifica: null,\n          storicoBobine: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Load storico bobine when selected\n  useEffect(() => {\n    if (cantiereId && selectedReportType === 'storico-bobine') {\n      loadStoricoBobine();\n    }\n  }, [cantiereId, selectedReportType]);\n\n  // Function to load storico bobine data\n  const loadStoricoBobine = async () => {\n    try {\n      setLoading(true);\n      // Chiamata API reale per ottenere i dati dello storico bobine\n      const response = await fetch(`/api/reports/${cantiereId}/storico-bobine?formato=video`);\n\n      if (!response.ok) {\n        throw new Error(`Errore HTTP: ${response.status}`);\n      }\n\n      const result = await response.json();\n\n      if (result.success && result.content) {\n        setReportsData(prev => ({\n          ...prev,\n          storicoBobine: result.content\n        }));\n      } else {\n        throw new Error(result.message || 'Errore nel caricamento dei dati');\n      }\n    } catch (err) {\n      console.error('Error loading storico bobine:', err);\n      setError('Errore nel caricamento dello storico bobine: ' + err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n\n\n  const renderProgressReport = (data) => (\n    <Box>\n      {/* Header con controlli migliorato */}\n      <Box sx={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 1,\n        border: '1px solid #e0e0e0'\n      }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📊 Report Avanzamento Lavori\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Metriche Principali - Cards Moderne con MetricCard */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Totali\"\n            value={data.metri_totali}\n            unit=\"m\"\n            subtitle=\"Lunghezza complessiva del progetto\"\n            gradient=\"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\"\n            size=\"large\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Posati\"\n            value={data.metri_posati}\n            unit=\"m\"\n            subtitle={`${data.percentuale_avanzamento}% completato`}\n            gradient=\"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\"\n            progress={data.percentuale_avanzamento}\n            trend={data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down'}\n            trendValue={`${data.percentuale_avanzamento}%`}\n            size=\"large\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Rimanenti\"\n            value={data.metri_da_posare}\n            unit=\"m\"\n            subtitle={`${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`}\n            gradient=\"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\"\n            size=\"large\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Media/Giorno\"\n            value={data.media_giornaliera || 0}\n            unit=\"m\"\n            subtitle={\n              data.giorni_stimati\n                ? `${data.giorni_stimati} giorni lavorativi rimasti`\n                : (data.media_giornaliera > 0\n                    ? 'Calcolo in corso'\n                    : 'Nessuna posa recente')\n            }\n            gradient=\"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\"\n            size=\"large\"\n            tooltip={\n              data.giorni_lavorativi_effettivi\n                ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.`\n                : 'Media giornaliera basata sui giorni di lavoro effettivo'\n            }\n          />\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <ProgressChart data={data} />\n        </Box>\n      )}\n\n      {/* Dettagli Performance - Cards Informative */}\n      <Grid container spacing={4} sx={{ mb: 5 }}>\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <CableIcon sx={{ color: '#3498db', mr: 1, fontSize: 28 }} />\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Stato Cavi\n                </Typography>\n              </Box>\n              <Grid container spacing={2}>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                      {data.totale_cavi}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>\n                      {data.cavi_posati}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Posati ({data.percentuale_cavi}%)\n                    </Typography>\n                  </Box>\n                </Grid>\n              </Grid>\n              <Box sx={{ mt: 2 }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body2\">Progresso</Typography>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                    {data.percentuale_cavi}%\n                  </Typography>\n                </Box>\n                <Box sx={{\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                }}>\n                  <Box sx={{\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }} />\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                <Box sx={{\n                  bgcolor: '#9c27b0',\n                  borderRadius: '50%',\n                  p: 1,\n                  mr: 2,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>\n                  <Typography variant=\"h6\" sx={{ color: 'white', fontWeight: 'bold' }}>\n                    🔒\n                  </Typography>\n                </Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Stato Certificazioni Cavi\n                </Typography>\n              </Box>\n\n              {data.certificazioni ? (\n                <>\n                  <Grid container spacing={2} sx={{ mb: 3 }}>\n                    <Grid item xs={6}>\n                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>\n                        <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>\n                          {data.certificazioni.totale}\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                          Certificati\n                        </Typography>\n                      </Box>\n                    </Grid>\n\n                    <Grid item xs={6}>\n                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#fff3cd', borderRadius: 1 }}>\n                        <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#856404', mb: 1 }}>\n                          {data.certificazioni.rimanenti}\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                          Da Certificare\n                        </Typography>\n                      </Box>\n                    </Grid>\n                  </Grid>\n\n                  <Box sx={{ textAlign: 'center', mb: 2 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#9c27b0', mb: 1 }}>\n                      {data.certificazioni.percentuale}%\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                      Completamento Certificazioni\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#999', fontSize: '0.75rem' }}>\n                      {data.certificazioni.oggi} certificazioni completate oggi\n                    </Typography>\n                  </Box>\n\n                  {/* Progress bar certificazioni */}\n                  <Box sx={{\n                    width: '100%',\n                    height: 8,\n                    bgcolor: '#e0e0e0',\n                    borderRadius: 4,\n                    overflow: 'hidden'\n                  }}>\n                    <Box sx={{\n                      width: `${data.certificazioni.percentuale}%`,\n                      height: '100%',\n                      bgcolor: '#9c27b0',\n                      transition: 'width 0.3s ease'\n                    }} />\n                  </Box>\n                </>\n              ) : (\n                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                  <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                    Nessuna certificazione disponibile\n                  </Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n\n\n      {/* Attività Recente - Design Migliorato */}\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Card sx={{ border: '1px solid #e0e0e0' }}>\n          <CardContent sx={{ p: 3 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n              <DateRangeIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                📈 Attività Recente\n              </Typography>\n            </Box>\n\n            {/* Mostra solo gli ultimi 5 record in formato card per mobile-friendly */}\n            <Grid container spacing={2}>\n              {data.posa_recente.slice(0, 5).map((posa, index) => (\n                <Grid item xs={12} sm={6} md={4} key={index}>\n                  <Box sx={{\n                    p: 2,\n                    border: '1px solid #e0e0e0',\n                    borderRadius: 2,\n                    bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                    transition: 'all 0.2s',\n                    '&:hover': {\n                      bgcolor: '#f5f5f5',\n                      transform: 'translateY(-2px)',\n                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                    }\n                  }}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                      {posa.data}\n                    </Typography>\n                    <Typography variant=\"h5\" sx={{ fontWeight: 700, color: '#2c3e50' }}>\n                      {posa.metri}m\n                    </Typography>\n                    {index === 0 && (\n                      <Chip\n                        label=\"Più recente\"\n                        size=\"small\"\n                        sx={{\n                          mt: 1,\n                          bgcolor: '#3498db',\n                          color: 'white',\n                          fontSize: '0.7rem'\n                        }}\n                      />\n                    )}\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n\n            {/* Link per vedere tutti i dati se ce ne sono di più */}\n            {data.posa_recente.length > 5 && (\n              <Box sx={{ mt: 3, textAlign: 'center' }}>\n                <Accordion>\n                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                    <Typography variant=\"body2\" sx={{ color: '#3498db' }}>\n                      Mostra tutti i {data.posa_recente.length} record\n                    </Typography>\n                  </AccordionSummary>\n                  <AccordionDetails>\n                    <FilterableTable\n                      data={data.posa_recente.map(posa => ({\n                        data: posa.data,\n                        metri: `${posa.metri}m`\n                      }))}\n                      columns={[\n                        { field: 'data', headerName: 'Data', width: 200 },\n                        { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }\n                      ]}\n                      pagination={true}\n                      pageSize={10}\n                    />\n                  </AccordionDetails>\n                </Accordion>\n              </Box>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </Box>\n  );\n\n  const renderBoqReport = (data) => (\n    <Box>\n      {/* Header migliorato */}\n      <Box sx={{\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 1,\n        border: '1px solid #e0e0e0'\n      }}>\n        <ListIcon sx={{ color: '#8e44ad', mr: 1, fontSize: 28 }} />\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📋 Bill of Quantities - Distinta Materiali\n        </Typography>\n      </Box>\n\n      {/* Grafici BOQ se disponibili */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <BoqChart data={data} />\n        </Box>\n      )}\n\n\n    </Box>\n  );\n  const renderStoricoBobineReport = (data) => {\n    const toggleBobina = (bobinaId) => {\n      const newExpanded = new Set(expandedBobine);\n      if (newExpanded.has(bobinaId)) {\n        newExpanded.delete(bobinaId);\n      } else {\n        newExpanded.add(bobinaId);\n      }\n      setExpandedBobine(newExpanded);\n    };\n\n    const getStatoColor = (stato) => {\n      switch (stato) {\n        case 'Disponibile': return '#27ae60';\n        case 'In Uso': return '#f39c12';\n        case 'Esaurita': return '#e74c3c';\n        default: return '#95a5a6';\n      }\n    };\n\n    return (\n      <Box>\n        {/* Header */}\n        <Box sx={{\n          display: 'flex',\n          alignItems: 'center',\n          mb: 3,\n          p: 2,\n          bgcolor: '#f8f9fa',\n          borderRadius: 1,\n          border: '1px solid #e0e0e0'\n        }}>\n          <TimelineIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />\n          <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n            📦 Storico Bobine - Cavi Associati\n          </Typography>\n        </Box>\n\n        {/* Lista Bobine con Accordion */}\n        <Box sx={{ mb: 3 }}>\n          {data.bobine.map((bobina) => (\n            <Paper key={bobina.id} sx={{ mb: 2, border: '1px solid #e0e0e0' }}>\n              {/* Header Bobina */}\n              <Box\n                sx={{\n                  p: 3,\n                  cursor: 'pointer',\n                  bgcolor: expandedBobine.has(bobina.id) ? '#f8f9fa' : 'white',\n                  borderBottom: expandedBobine.has(bobina.id) ? '1px solid #e0e0e0' : 'none',\n                  '&:hover': {\n                    bgcolor: '#f5f5f5'\n                  }\n                }}\n                onClick={() => toggleBobina(bobina.id)}\n              >\n                <Grid container spacing={2} alignItems=\"center\">\n                  <Grid item xs={12} sm={3}>\n                    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                      <Box sx={{\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        bgcolor: getStatoColor(bobina.stato),\n                        mr: 2\n                      }} />\n                      <Box>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                          {bobina.codice}\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                          {bobina.tipologia}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </Grid>\n\n                  <Grid item xs={12} sm={2}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                      Formazione\n                    </Typography>\n                    <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                      {bobina.formazione}\n                    </Typography>\n                  </Grid>\n\n                  <Grid item xs={12} sm={2}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                      Metri Totali\n                    </Typography>\n                    <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                      {bobina.metri_totali}m\n                    </Typography>\n                  </Grid>\n\n                  <Grid item xs={12} sm={2}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                      Utilizzati\n                    </Typography>\n                    <Typography variant=\"body1\" sx={{ fontWeight: 500, color: '#e74c3c' }}>\n                      {bobina.metri_utilizzati}m\n                    </Typography>\n                  </Grid>\n\n                  <Grid item xs={12} sm={2}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                      Residui\n                    </Typography>\n                    <Typography variant=\"body1\" sx={{ fontWeight: 500, color: '#27ae60' }}>\n                      {bobina.metri_residui}m\n                    </Typography>\n                  </Grid>\n\n                  <Grid item xs={12} sm={1}>\n                    <Box sx={{ display: 'flex', justifyContent: 'center' }}>\n                      {expandedBobine.has(bobina.id) ?\n                        <ExpandLessIcon sx={{ color: '#666' }} /> :\n                        <ExpandMoreIcon sx={{ color: '#666' }} />\n                      }\n                    </Box>\n                  </Grid>\n                </Grid>\n              </Box>\n\n              {/* Dettagli Cavi Associati */}\n              {expandedBobine.has(bobina.id) && (\n                <Box sx={{ p: 3, bgcolor: '#fafafa' }}>\n                  <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: '#2c3e50' }}>\n                    Cavi Associati ({bobina.cavi_associati.length})\n                  </Typography>\n\n                  {bobina.cavi_associati.length > 0 ? (\n                    <Grid container spacing={2}>\n                      {bobina.cavi_associati.map((cavo) => (\n                        <Grid item xs={12} sm={6} md={4} key={cavo.id}>\n                          <Paper sx={{ p: 2, border: '1px solid #e0e0e0', bgcolor: 'white' }}>\n                            <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 1 }}>\n                              {cavo.nomenclatura}\n                            </Typography>\n                            <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                              Metri utilizzati: <strong>{cavo.metri_utilizzati}m</strong>\n                            </Typography>\n                            <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                              Data posa: {new Date(cavo.data_posa).toLocaleDateString('it-IT')}\n                            </Typography>\n                          </Paper>\n                        </Grid>\n                      ))}\n                    </Grid>\n                  ) : (\n                    <Box sx={{ textAlign: 'center', p: 3, bgcolor: 'white', borderRadius: 1 }}>\n                      <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                        Nessun cavo associato a questa bobina\n                      </Typography>\n                    </Box>\n                  )}\n\n                  {/* Info aggiuntive bobina */}\n                  <Box sx={{ mt: 3, p: 2, bgcolor: 'white', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} sm={4}>\n                        <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                          Fornitore\n                        </Typography>\n                        <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                          {bobina.fornitore}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} sm={4}>\n                        <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                          Data Arrivo\n                        </Typography>\n                        <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                          {new Date(bobina.data_arrivo).toLocaleDateString('it-IT')}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} sm={4}>\n                        <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                          Stato\n                        </Typography>\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Box sx={{\n                            width: 8,\n                            height: 8,\n                            borderRadius: '50%',\n                            bgcolor: getStatoColor(bobina.stato),\n                            mr: 1\n                          }} />\n                          <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                            {bobina.stato}\n                          </Typography>\n                        </Box>\n                      </Grid>\n                    </Grid>\n                  </Box>\n                </Box>\n              )}\n            </Paper>\n          ))}\n        </Box>\n\n        {/* Riepilogo */}\n        <Paper sx={{ p: 3, bgcolor: '#f8f9fa', border: '1px solid #e0e0e0' }}>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: '#2c3e50' }}>\n            📊 Riepilogo Generale\n          </Typography>\n          <Grid container spacing={3}>\n            <Grid item xs={12} sm={3}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                  {data.bobine.length}\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                  Bobine Totali\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} sm={3}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                  {data.bobine.reduce((sum, b) => sum + b.cavi_associati.length, 0)}\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                  Cavi Associati\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} sm={3}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                  {data.bobine.reduce((sum, b) => sum + b.metri_utilizzati, 0)}m\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                  Metri Utilizzati\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} sm={3}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                  {data.bobine.reduce((sum, b) => sum + b.metri_residui, 0)}m\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                  Metri Residui\n                </Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </Paper>\n      </Box>\n    );\n  };\n\n\n\n\n\n  const renderPosaPeriodoReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n          Report Posa per Periodo\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Statistiche Periodo - Layout Orizzontale */}\n      <Grid container spacing={4} sx={{ mb: 5 }}>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.totale_metri_periodo}m\n            </Typography>\n            <Typography variant=\"body1\">Metri Totali</Typography>\n            <Typography variant=\"caption\">{data.data_inizio} - {data.data_fine}</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.giorni_attivi}\n            </Typography>\n            <Typography variant=\"body1\">Giorni Attivi</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.media_giornaliera}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Giorno</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {Math.round(data.totale_metri_periodo / data.giorni_attivi * 7)}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Settimana</Typography>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 5, width: '100%' }}>\n          <TimelineChart data={data} />\n        </Box>\n      )}\n\n      {/* Posa Giornaliera */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n          Dettaglio Posa Giornaliera\n        </Typography>\n        <FilterableTable\n          data={data.posa_giornaliera || []}\n          columns={[\n            { field: 'data', headerName: 'Data', width: 200 },\n            { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri}m` }\n          ]}\n          pageSize={10}\n        />\n      </Paper>\n    </Box>\n  );\n\n\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {dialogType === 'posa-periodo' ? 'Report Posa per Periodo' : 'Genera Report'}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box className=\"report-main-container report-fade-in\">\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports Navigation */}\n      <Box sx={{ mt: 3 }}>\n        {/* Report Navigation - Design Compatto */}\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50', mb: 2, textAlign: 'center' }}>\n            🎯 Seleziona il tipo di report\n          </Typography>\n          <Grid container spacing={2}>\n            {/* Report Avanzamento */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                className={`report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`}\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('progress')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <AssessmentIcon sx={{ fontSize: 32, color: '#3498db', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Avanzamento\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Panoramica lavori\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {/* Bill of Quantities */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('boq')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <ListIcon sx={{ fontSize: 32, color: '#8e44ad', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Bill of Quantities\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Distinta materiali\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n\n\n            {/* Storico Bobine */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'storico-bobine' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'storico-bobine' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('storico-bobine')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <TimelineIcon sx={{ fontSize: 32, color: '#9b59b6', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Storico Bobine\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Cavi per bobina\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n\n        {/* Report Content */}\n        <Box sx={{ minHeight: '400px', width: '100%' }}>\n          {/* Progress Report */}\n          {selectedReportType === 'progress' && (\n            <Paper sx={{ p: 2, borderRadius: 2 }}>\n              {reportsData.progress ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderProgressReport(reportsData.progress)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"progress\"\n                  title=\"Caricamento Report Avanzamento...\"\n                  description=\"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"progress\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getProgressReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          progress: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying progress report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Bill of Quantities */}\n          {selectedReportType === 'boq' && (\n            <Paper sx={{ p: 2, borderRadius: 2 }}>\n              {reportsData.boq ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderBoqReport(reportsData.boq)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"boq\"\n                  title=\"Caricamento Bill of Quantities...\"\n                  description=\"Stiamo elaborando la distinta materiali\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"boq\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getBillOfQuantities(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          boq: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying BOQ report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Storico Bobine Report */}\n          {selectedReportType === 'storico-bobine' && (\n            <Paper sx={{ p: 2, borderRadius: 2 }}>\n              {reportsData.storicoBobine ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('storico-bobine', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('storico-bobine', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderStoricoBobineReport(reportsData.storicoBobine)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"storico-bobine\"\n                  title=\"Caricamento Storico Bobine...\"\n                  description=\"Stiamo elaborando i dati delle bobine e dei cavi associati\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"storico-bobine\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare lo storico bobine. Verifica la connessione e riprova.\"\n                  onRetry={() => loadStoricoBobine()}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Posa per Periodo Report */}\n          {selectedReportType === 'posa-periodo' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.posaPeriodo ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n                </Box>\n              ) : (\n                <EmptyState\n                  type=\"action-required\"\n                  reportType=\"posa-periodo\"\n                  title=\"Seleziona un Periodo\"\n                  description=\"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttività del team.\"\n                  actionLabel=\"Seleziona Periodo\"\n                  onAction={() => {\n                    setDialogType('posa-periodo');\n                    // Set default date range (last month to today)\n                    const today = new Date();\n                    const lastMonth = new Date();\n                    lastMonth.setMonth(today.getMonth() - 1);\n\n                    setFormData({\n                      ...formData,\n                      data_inizio: lastMonth.toISOString().split('T')[0],\n                      data_fine: today.toISOString().split('T')[0]\n                    });\n                    setOpenDialog(true);\n                  }}\n                />\n              )}\n            </Paper>\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,0BAA0B;AACjC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EAEXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAEhBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAE5BC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAG5BC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAElBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,UAAU,MAAM,oCAAoC;;AAG3D;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAW,CAAC,GAAGf,SAAS,CAAC,CAAC;EAElC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgE,KAAK,EAAEC,QAAQ,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAACkE,UAAU,EAAEC,aAAa,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvE,QAAQ,CAAC,UAAU,CAAC;EACxE,MAAM,CAACwE,QAAQ,EAAEC,WAAW,CAAC,GAAGzE,QAAQ,CAAC;IACvC0E,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC;IAC7CgF,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,eAAe,EAAE,IAAI;IACrBC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACsF,cAAc,EAAEC,iBAAiB,CAAC,GAAGvF,QAAQ,CAAC,IAAIwF,GAAG,CAAC,CAAC,CAAC;;EAE/D;EACAvF,SAAS,CAAC,MAAM;IACd,MAAMwF,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC1B,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAM2B,qBAAqB,GAAG,MAAM,MAAM,CAAC,sCAAsC,CAAC;;QAElF;QACA,MAAMC,eAAe,GAAG3C,aAAa,CAAC4C,iBAAiB,CAAC/B,UAAU,EAAE,OAAO,CAAC,CACzEgC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,gCAAgC,EAAE8B,GAAG,CAAC;UACpD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMC,UAAU,GAAGjD,aAAa,CAACkD,mBAAmB,CAACrC,UAAU,EAAE,OAAO,CAAC,CACtEgC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,2BAA2B,EAAE8B,GAAG,CAAC;UAC/C,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;;QAEJ;QACA,MAAMG,qBAAqB,GAAGT,qBAAqB,CAACU,OAAO,CAACC,iBAAiB,CAACxC,UAAU,CAAC,CACtFgC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,+BAA+B,EAAE8B,GAAG,CAAC;UACnD,OAAO,EAAE;QACX,CAAC,CAAC;;QAEJ;QACA,MAAM,CAACQ,YAAY,EAAEC,OAAO,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpEf,eAAe,EACfM,UAAU,EACVE,qBAAqB,CACtB,CAAC;;QAEF;QACA,IAAIG,YAAY,CAACN,OAAO,IAAIQ,kBAAkB,EAAE;UAC9C,MAAMG,UAAU,GAAGL,YAAY,CAACN,OAAO,CAACY,WAAW,IAAI,CAAC;UACxD,MAAMC,eAAe,GAAGL,kBAAkB,CAACM,MAAM;UACjD,MAAMC,yBAAyB,GAAGJ,UAAU,GAAG,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAEJ,eAAe,GAAGF,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;UAEvG;UACA,MAAMO,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;UACtC,MAAMC,kBAAkB,GAAGb,kBAAkB,CAACc,MAAM,CAACC,IAAI,IACvD,IAAIJ,IAAI,CAACI,IAAI,CAACC,mBAAmB,CAAC,CAACJ,YAAY,CAAC,CAAC,KAAKF,IACxD,CAAC,CAACJ,MAAM;UAERR,YAAY,CAACN,OAAO,CAACyB,cAAc,GAAG;YACpCC,MAAM,EAAEb,eAAe;YACvBc,WAAW,EAAEZ,yBAAyB;YACtCG,IAAI,EAAEG,kBAAkB;YACxBO,SAAS,EAAEjB,UAAU,GAAGE;UAC1B,CAAC;QACH;;QAEA;QACA9B,cAAc,CAAC;UACbC,QAAQ,EAAEsB,YAAY,CAACN,OAAO;UAC9Bf,GAAG,EAAEsB,OAAO,CAACP,OAAO;UACpBd,eAAe,EAAE,IAAI;UACrBC,aAAa,EAAE;QACjB,CAAC,CAAC;;QAEF;QACA,IAAImB,YAAY,CAACN,OAAO,IAAIO,OAAO,CAACP,OAAO,EAAE;UAC3C/B,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLA,QAAQ,CAAC,uDAAuD,CAAC;QACnE;MACF,CAAC,CAAC,OAAO6B,GAAG,EAAE;QACZ;QACAC,OAAO,CAAC/B,KAAK,CAAC,mCAAmC,EAAE8B,GAAG,CAAC;QACvD7B,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIF,UAAU,EAAE;MACd4B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC5B,UAAU,CAAC,CAAC;;EAEhB;EACA5D,SAAS,CAAC,MAAM;IACd,IAAI4D,UAAU,IAAIS,kBAAkB,KAAK,gBAAgB,EAAE;MACzDuD,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAChE,UAAU,EAAES,kBAAkB,CAAC,CAAC;;EAEpC;EACA,MAAMuD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF9D,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAM+D,QAAQ,GAAG,MAAMC,KAAK,CAAC,gBAAgBlE,UAAU,+BAA+B,CAAC;MAEvF,IAAI,CAACiE,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,gBAAgBH,QAAQ,CAACI,MAAM,EAAE,CAAC;MACpD;MAEA,MAAMC,MAAM,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAEpC,IAAID,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACnC,OAAO,EAAE;QACpCjB,cAAc,CAACuD,IAAI,KAAK;UACtB,GAAGA,IAAI;UACPnD,aAAa,EAAEgD,MAAM,CAACnC;QACxB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAIiC,KAAK,CAACE,MAAM,CAACI,OAAO,IAAI,iCAAiC,CAAC;MACtE;IACF,CAAC,CAAC,OAAOzC,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,+BAA+B,EAAE8B,GAAG,CAAC;MACnD7B,QAAQ,CAAC,+CAA+C,GAAG6B,GAAG,CAACyC,OAAO,CAAC;IACzE,CAAC,SAAS;MACRxE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyE,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACF3E,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI6D,QAAQ;MAEZ,QAAQW,UAAU;QAChB,KAAK,UAAU;UACbX,QAAQ,GAAG,MAAM9E,aAAa,CAAC4C,iBAAiB,CAAC/B,UAAU,EAAE6E,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRZ,QAAQ,GAAG,MAAM9E,aAAa,CAACkD,mBAAmB,CAACrC,UAAU,EAAE6E,MAAM,CAAC;UACtE;QAEF,KAAK,cAAc;UACjB,IAAI,CAAClE,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDX,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACA6D,QAAQ,GAAG,MAAM9E,aAAa,CAAC2F,uBAAuB,CACpD9E,UAAU,EACVW,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClB8D,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAIT,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIS,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtE1D,cAAc,CAACuD,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACG,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGX,QAAQ,CAAC9B;UACpF,CAAC,CAAC,CAAC;QACL;MACF,CAAC,MAAM;QACL;QACA,IAAI8B,QAAQ,CAACc,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAAChB,QAAQ,CAACc,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAO9C,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,sCAAsC,EAAE8B,GAAG,CAAC;MAC1D7B,QAAQ,CAAC6B,GAAG,CAACiD,MAAM,IAAIjD,GAAG,CAACyC,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRxE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAID,MAAMiF,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMR,wBAAwB,CAACpE,UAAU,EAAEI,QAAQ,CAACE,OAAO,CAAC;IAC5DP,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAM8E,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9E,aAAa,CAAC,KAAK,CAAC;IACpBF,QAAQ,CAAC,IAAI,CAAC;IACdQ,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAID,MAAMqE,oBAAoB,GAAIC,IAAI,iBAChC3F,OAAA,CAACtD,GAAG;IAAAkJ,QAAA,gBAEF5F,OAAA,CAACtD,GAAG;MAACmJ,EAAE,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAAT,QAAA,gBACA5F,OAAA,CAACrD,UAAU;QAAC2J,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEU,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAZ,QAAA,EAAC;MAEpE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5G,OAAA,CAAC/B,gBAAgB;QACf4I,OAAO,eACL7G,OAAA,CAAChC,MAAM;UACL8I,OAAO,EAAElF,UAAW;UACpBmF,QAAQ,EAAGC,CAAC,IAAKnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDN,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDM,KAAK,eACHlH,OAAA,CAACtD,GAAG;UAACmJ,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,gBACjD5F,OAAA,CAACX,aAAa;YAACwG,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5G,OAAA,CAACnD,IAAI;MAACuK,SAAS;MAACC,OAAO,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxC5F,OAAA,CAACnD,IAAI;QAACyK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9B5F,OAAA,CAACL,UAAU;UACT+H,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEhC,IAAI,CAACiC,YAAa;UACzBC,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAC,oCAAoC;UAC7CC,QAAQ,EAAC,mDAAmD;UAC5DC,IAAI,EAAC;QAAO;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEP5G,OAAA,CAACnD,IAAI;QAACyK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9B5F,OAAA,CAACL,UAAU;UACT+H,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEhC,IAAI,CAACsC,YAAa;UACzBJ,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAE,GAAGnC,IAAI,CAACuC,uBAAuB,cAAe;UACxDH,QAAQ,EAAC,mDAAmD;UAC5DvG,QAAQ,EAAEmE,IAAI,CAACuC,uBAAwB;UACvCC,KAAK,EAAExC,IAAI,CAACuC,uBAAuB,GAAG,EAAE,GAAG,IAAI,GAAGvC,IAAI,CAACuC,uBAAuB,GAAG,EAAE,GAAG,MAAM,GAAG,MAAO;UACtGE,UAAU,EAAE,GAAGzC,IAAI,CAACuC,uBAAuB,GAAI;UAC/CF,IAAI,EAAC;QAAO;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEP5G,OAAA,CAACnD,IAAI;QAACyK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9B5F,OAAA,CAACL,UAAU;UACT+H,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAEhC,IAAI,CAAC0C,eAAgB;UAC5BR,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAE,GAAG,CAAC,GAAG,GAAGnC,IAAI,CAACuC,uBAAuB,EAAEI,OAAO,CAAC,CAAC,CAAC,iBAAkB;UAC9EP,QAAQ,EAAC,mDAAmD;UAC5DC,IAAI,EAAC;QAAO;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEP5G,OAAA,CAACnD,IAAI;QAACyK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9B5F,OAAA,CAACL,UAAU;UACT+H,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEhC,IAAI,CAAC4C,iBAAiB,IAAI,CAAE;UACnCV,IAAI,EAAC,GAAG;UACRC,QAAQ,EACNnC,IAAI,CAAC6C,cAAc,GACf,GAAG7C,IAAI,CAAC6C,cAAc,4BAA4B,GACjD7C,IAAI,CAAC4C,iBAAiB,GAAG,CAAC,GACvB,kBAAkB,GAClB,sBACT;UACDR,QAAQ,EAAC,mDAAmD;UAC5DC,IAAI,EAAC,OAAO;UACZS,OAAO,EACL9C,IAAI,CAAC+C,2BAA2B,GAC5B,gBAAgB/C,IAAI,CAAC+C,2BAA2B,oFAAoF,GACpI;QACL;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGNhF,UAAU,iBACT5B,OAAA,CAACtD,GAAG;MAACmJ,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACjB5F,OAAA,CAACJ,aAAa;QAAC+F,IAAI,EAAEA;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGD5G,OAAA,CAACnD,IAAI;MAACuK,SAAS;MAACC,OAAO,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxC5F,OAAA,CAACnD,IAAI;QAACyK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvB5F,OAAA,CAAClD,IAAI;UAAC+I,EAAE,EAAE;YAAE8C,MAAM,EAAE,MAAM;YAAEtC,MAAM,EAAE;UAAoB,CAAE;UAAAT,QAAA,eACxD5F,OAAA,CAACjD,WAAW;YAAC8I,EAAE,EAAE;cAAEK,CAAC,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACxB5F,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACxD5F,OAAA,CAACjB,SAAS;gBAAC8G,EAAE,EAAE;kBAAEW,KAAK,EAAE,SAAS;kBAAEW,EAAE,EAAE,CAAC;kBAAEyB,QAAQ,EAAE;gBAAG;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5D5G,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,EAAC;cAEpE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5G,OAAA,CAACnD,IAAI;cAACuK,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAzB,QAAA,gBACzB5F,OAAA,CAACnD,IAAI;gBAACyK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACf5F,OAAA,CAACtD,GAAG;kBAACmJ,EAAE,EAAE;oBAAEgD,SAAS,EAAE,QAAQ;oBAAE3C,CAAC,EAAE,CAAC;oBAAEC,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAR,QAAA,gBAC1E5F,OAAA,CAACrD,UAAU;oBAAC2J,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE,GAAG;sBAAEC,KAAK,EAAE,SAAS;sBAAEP,EAAE,EAAE;oBAAE,CAAE;oBAAAL,QAAA,EACvED,IAAI,CAACvC;kBAAW;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACb5G,OAAA,CAACrD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO,CAAE;oBAAAZ,QAAA,EAAC;kBAEnD;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACP5G,OAAA,CAACnD,IAAI;gBAACyK,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACf5F,OAAA,CAACtD,GAAG;kBAACmJ,EAAE,EAAE;oBAAEgD,SAAS,EAAE,QAAQ;oBAAE3C,CAAC,EAAE,CAAC;oBAAEC,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAR,QAAA,gBAC1E5F,OAAA,CAACrD,UAAU;oBAAC2J,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE,GAAG;sBAAEC,KAAK,EAAE,SAAS;sBAAEP,EAAE,EAAE;oBAAE,CAAE;oBAAAL,QAAA,EACvED,IAAI,CAACmD;kBAAW;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACb5G,OAAA,CAACrD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO,CAAE;oBAAAZ,QAAA,GAAC,eACpC,EAACD,IAAI,CAACoD,gBAAgB,EAAC,IACtC;kBAAA;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACP5G,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEmD,EAAE,EAAE;cAAE,CAAE;cAAApD,QAAA,gBACjB5F,OAAA,CAACtD,GAAG;gBAACmJ,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACnE5F,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAAAV,QAAA,EAAC;gBAAS;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClD5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE;kBAAI,CAAE;kBAAAX,QAAA,GACjDD,IAAI,CAACoD,gBAAgB,EAAC,GACzB;gBAAA;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN5G,OAAA,CAACtD,GAAG;gBAACmJ,EAAE,EAAE;kBACPoD,KAAK,EAAE,MAAM;kBACbN,MAAM,EAAE,CAAC;kBACTxC,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,CAAC;kBACf8C,QAAQ,EAAE;gBACZ,CAAE;gBAAAtD,QAAA,eACA5F,OAAA,CAACtD,GAAG;kBAACmJ,EAAE,EAAE;oBACPoD,KAAK,EAAE,GAAGtD,IAAI,CAACoD,gBAAgB,GAAG;oBAClCJ,MAAM,EAAE,MAAM;oBACdxC,OAAO,EAAE,SAAS;oBAClBgD,UAAU,EAAE;kBACd;gBAAE;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP5G,OAAA,CAACnD,IAAI;QAACyK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvB5F,OAAA,CAAClD,IAAI;UAAC+I,EAAE,EAAE;YAAE8C,MAAM,EAAE,MAAM;YAAEtC,MAAM,EAAE;UAAoB,CAAE;UAAAT,QAAA,eACxD5F,OAAA,CAACjD,WAAW;YAAC8I,EAAE,EAAE;cAAEK,CAAC,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACxB5F,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACxD5F,OAAA,CAACtD,GAAG;gBAACmJ,EAAE,EAAE;kBACPM,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,KAAK;kBACnBF,CAAC,EAAE,CAAC;kBACJiB,EAAE,EAAE,CAAC;kBACLrB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE;gBAClB,CAAE;gBAAAH,QAAA,eACA5F,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,OAAO;oBAAED,UAAU,EAAE;kBAAO,CAAE;kBAAAX,QAAA,EAAC;gBAErE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN5G,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,EAAC;cAEpE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAELjB,IAAI,CAAC1B,cAAc,gBAClBjE,OAAA,CAAAE,SAAA;cAAA0F,QAAA,gBACE5F,OAAA,CAACnD,IAAI;gBAACuK,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAACxB,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACxC5F,OAAA,CAACnD,IAAI;kBAACyK,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAA3B,QAAA,eACf5F,OAAA,CAACtD,GAAG;oBAACmJ,EAAE,EAAE;sBAAEgD,SAAS,EAAE,QAAQ;sBAAE3C,CAAC,EAAE,CAAC;sBAAEC,OAAO,EAAE,SAAS;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAAR,QAAA,gBAC1E5F,OAAA,CAACrD,UAAU;sBAAC2J,OAAO,EAAC,IAAI;sBAACT,EAAE,EAAE;wBAAEU,UAAU,EAAE,GAAG;wBAAEC,KAAK,EAAE,SAAS;wBAAEP,EAAE,EAAE;sBAAE,CAAE;sBAAAL,QAAA,EACvED,IAAI,CAAC1B,cAAc,CAACC;oBAAM;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACb5G,OAAA,CAACrD,UAAU;sBAAC2J,OAAO,EAAC,OAAO;sBAACT,EAAE,EAAE;wBAAEW,KAAK,EAAE;sBAAO,CAAE;sBAAAZ,QAAA,EAAC;oBAEnD;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAEP5G,OAAA,CAACnD,IAAI;kBAACyK,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAA3B,QAAA,eACf5F,OAAA,CAACtD,GAAG;oBAACmJ,EAAE,EAAE;sBAAEgD,SAAS,EAAE,QAAQ;sBAAE3C,CAAC,EAAE,CAAC;sBAAEC,OAAO,EAAE,SAAS;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAAR,QAAA,gBAC1E5F,OAAA,CAACrD,UAAU;sBAAC2J,OAAO,EAAC,IAAI;sBAACT,EAAE,EAAE;wBAAEU,UAAU,EAAE,GAAG;wBAAEC,KAAK,EAAE,SAAS;wBAAEP,EAAE,EAAE;sBAAE,CAAE;sBAAAL,QAAA,EACvED,IAAI,CAAC1B,cAAc,CAACG;oBAAS;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,eACb5G,OAAA,CAACrD,UAAU;sBAAC2J,OAAO,EAAC,OAAO;sBAACT,EAAE,EAAE;wBAAEW,KAAK,EAAE;sBAAO,CAAE;sBAAAZ,QAAA,EAAC;oBAEnD;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEP5G,OAAA,CAACtD,GAAG;gBAACmJ,EAAE,EAAE;kBAAEgD,SAAS,EAAE,QAAQ;kBAAE5C,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACtC5F,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE,CAAE;kBAAAL,QAAA,GACvED,IAAI,CAAC1B,cAAc,CAACE,WAAW,EAAC,GACnC;gBAAA;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEP,EAAE,EAAE;kBAAE,CAAE;kBAAAL,QAAA,EAAC;gBAE1D;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,SAAS;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEoC,QAAQ,EAAE;kBAAU,CAAE;kBAAAhD,QAAA,GACtED,IAAI,CAAC1B,cAAc,CAACP,IAAI,EAAC,iCAC5B;gBAAA;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGN5G,OAAA,CAACtD,GAAG;gBAACmJ,EAAE,EAAE;kBACPoD,KAAK,EAAE,MAAM;kBACbN,MAAM,EAAE,CAAC;kBACTxC,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,CAAC;kBACf8C,QAAQ,EAAE;gBACZ,CAAE;gBAAAtD,QAAA,eACA5F,OAAA,CAACtD,GAAG;kBAACmJ,EAAE,EAAE;oBACPoD,KAAK,EAAE,GAAGtD,IAAI,CAAC1B,cAAc,CAACE,WAAW,GAAG;oBAC5CwE,MAAM,EAAE,MAAM;oBACdxC,OAAO,EAAE,SAAS;oBAClBgD,UAAU,EAAE;kBACd;gBAAE;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,eACN,CAAC,gBAEH5G,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEgD,SAAS,EAAE,QAAQ;gBAAE3C,CAAC,EAAE,CAAC;gBAAEC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAR,QAAA,eAC1E5F,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAKNjB,IAAI,CAACyD,YAAY,IAAIzD,IAAI,CAACyD,YAAY,CAAC9F,MAAM,GAAG,CAAC,iBAChDtD,OAAA,CAAClD,IAAI;MAAC+I,EAAE,EAAE;QAAEQ,MAAM,EAAE;MAAoB,CAAE;MAAAT,QAAA,eACxC5F,OAAA,CAACjD,WAAW;QAAC8I,EAAE,EAAE;UAAEK,CAAC,EAAE;QAAE,CAAE;QAAAN,QAAA,gBACxB5F,OAAA,CAACtD,GAAG;UAACmJ,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAL,QAAA,gBACxD5F,OAAA,CAACnB,aAAa;YAACgH,EAAE,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEW,EAAE,EAAE,CAAC;cAAEyB,QAAQ,EAAE;YAAG;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChE5G,OAAA,CAACrD,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAZ,QAAA,EAAC;UAEpE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGN5G,OAAA,CAACnD,IAAI;UAACuK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,EACxBD,IAAI,CAACyD,YAAY,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7CxJ,OAAA,CAACnD,IAAI;YAACyK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC9B5F,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBACPK,CAAC,EAAE,CAAC;gBACJG,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,CAAC;gBACfD,OAAO,EAAEqD,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;gBAC5CL,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE;kBACThD,OAAO,EAAE,SAAS;kBAClBsD,SAAS,EAAE,kBAAkB;kBAC7BC,SAAS,EAAE;gBACb;cACF,CAAE;cAAA9D,QAAA,gBACA5F,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE,MAAM;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,EACtD2D,IAAI,CAAC5D;cAAI;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACb5G,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,GAChE2D,IAAI,CAACI,KAAK,EAAC,GACd;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ4C,KAAK,KAAK,CAAC,iBACVxJ,OAAA,CAAC/C,IAAI;gBACHiK,KAAK,EAAC,gBAAa;gBACnBc,IAAI,EAAC,OAAO;gBACZnC,EAAE,EAAE;kBACFmD,EAAE,EAAE,CAAC;kBACL7C,OAAO,EAAE,SAAS;kBAClBK,KAAK,EAAE,OAAO;kBACdoC,QAAQ,EAAE;gBACZ;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GA/B8B4C,KAAK;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGNjB,IAAI,CAACyD,YAAY,CAAC9F,MAAM,GAAG,CAAC,iBAC3BtD,OAAA,CAACtD,GAAG;UAACmJ,EAAE,EAAE;YAAEmD,EAAE,EAAE,CAAC;YAAEH,SAAS,EAAE;UAAS,CAAE;UAAAjD,QAAA,eACtC5F,OAAA,CAACnC,SAAS;YAAA+H,QAAA,gBACR5F,OAAA,CAAClC,gBAAgB;cAAC8L,UAAU,eAAE5J,OAAA,CAACf,cAAc;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAhB,QAAA,eAC/C5F,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,GAAC,iBACrC,EAACD,IAAI,CAACyD,YAAY,CAAC9F,MAAM,EAAC,SAC3C;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACnB5G,OAAA,CAACjC,gBAAgB;cAAA6H,QAAA,eACf5F,OAAA,CAACP,eAAe;gBACdkG,IAAI,EAAEA,IAAI,CAACyD,YAAY,CAACE,GAAG,CAACC,IAAI,KAAK;kBACnC5D,IAAI,EAAE4D,IAAI,CAAC5D,IAAI;kBACfgE,KAAK,EAAE,GAAGJ,IAAI,CAACI,KAAK;gBACtB,CAAC,CAAC,CAAE;gBACJE,OAAO,EAAE,CACP;kBAAEC,KAAK,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEd,KAAK,EAAE;gBAAI,CAAC,EACjD;kBAAEa,KAAK,EAAE,OAAO;kBAAEC,UAAU,EAAE,cAAc;kBAAEd,KAAK,EAAE,GAAG;kBAAEe,KAAK,EAAE;gBAAQ,CAAC,CAC1E;gBACFC,UAAU,EAAE,IAAK;gBACjBC,QAAQ,EAAE;cAAG;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMuD,eAAe,GAAIxE,IAAI,iBAC3B3F,OAAA,CAACtD,GAAG;IAAAkJ,QAAA,gBAEF5F,OAAA,CAACtD,GAAG;MAACmJ,EAAE,EAAE;QACPC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAAT,QAAA,gBACA5F,OAAA,CAACzB,QAAQ;QAACsH,EAAE,EAAE;UAAEW,KAAK,EAAE,SAAS;UAAEW,EAAE,EAAE,CAAC;UAAEyB,QAAQ,EAAE;QAAG;MAAE;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3D5G,OAAA,CAACrD,UAAU;QAAC2J,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEU,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAZ,QAAA,EAAC;MAEpE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGLhF,UAAU,iBACT5B,OAAA,CAACtD,GAAG;MAACmJ,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACjB5F,OAAA,CAACH,QAAQ;QAAC8F,IAAI,EAAEA;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGE,CACN;EACD,MAAMwD,yBAAyB,GAAIzE,IAAI,IAAK;IAC1C,MAAM0E,YAAY,GAAIC,QAAQ,IAAK;MACjC,MAAMC,WAAW,GAAG,IAAIvI,GAAG,CAACF,cAAc,CAAC;MAC3C,IAAIyI,WAAW,CAACC,GAAG,CAACF,QAAQ,CAAC,EAAE;QAC7BC,WAAW,CAACE,MAAM,CAACH,QAAQ,CAAC;MAC9B,CAAC,MAAM;QACLC,WAAW,CAACG,GAAG,CAACJ,QAAQ,CAAC;MAC3B;MACAvI,iBAAiB,CAACwI,WAAW,CAAC;IAChC,CAAC;IAED,MAAMI,aAAa,GAAIC,KAAK,IAAK;MAC/B,QAAQA,KAAK;QACX,KAAK,aAAa;UAAE,OAAO,SAAS;QACpC,KAAK,QAAQ;UAAE,OAAO,SAAS;QAC/B,KAAK,UAAU;UAAE,OAAO,SAAS;QACjC;UAAS,OAAO,SAAS;MAC3B;IACF,CAAC;IAED,oBACE5K,OAAA,CAACtD,GAAG;MAAAkJ,QAAA,gBAEF5F,OAAA,CAACtD,GAAG;QAACmJ,EAAE,EAAE;UACPC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,EAAE,EAAE,CAAC;UACLC,CAAC,EAAE,CAAC;UACJC,OAAO,EAAE,SAAS;UAClBC,YAAY,EAAE,CAAC;UACfC,MAAM,EAAE;QACV,CAAE;QAAAT,QAAA,gBACA5F,OAAA,CAAC3B,YAAY;UAACwH,EAAE,EAAE;YAAEW,KAAK,EAAE,SAAS;YAAEW,EAAE,EAAE,CAAC;YAAEyB,QAAQ,EAAE;UAAG;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/D5G,OAAA,CAACrD,UAAU;UAAC2J,OAAO,EAAC,IAAI;UAACT,EAAE,EAAE;YAAEU,UAAU,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAZ,QAAA,EAAC;QAEpE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN5G,OAAA,CAACtD,GAAG;QAACmJ,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,EAChBD,IAAI,CAACkF,MAAM,CAACvB,GAAG,CAAEwB,MAAM,iBACtB9K,OAAA,CAACpD,KAAK;UAAiBiJ,EAAE,EAAE;YAAEI,EAAE,EAAE,CAAC;YAAEI,MAAM,EAAE;UAAoB,CAAE;UAAAT,QAAA,gBAEhE5F,OAAA,CAACtD,GAAG;YACFmJ,EAAE,EAAE;cACFK,CAAC,EAAE,CAAC;cACJ6E,MAAM,EAAE,SAAS;cACjB5E,OAAO,EAAErE,cAAc,CAAC0I,GAAG,CAACM,MAAM,CAACE,EAAE,CAAC,GAAG,SAAS,GAAG,OAAO;cAC5DC,YAAY,EAAEnJ,cAAc,CAAC0I,GAAG,CAACM,MAAM,CAACE,EAAE,CAAC,GAAG,mBAAmB,GAAG,MAAM;cAC1E,SAAS,EAAE;gBACT7E,OAAO,EAAE;cACX;YACF,CAAE;YACF+E,OAAO,EAAEA,CAAA,KAAMb,YAAY,CAACS,MAAM,CAACE,EAAE,CAAE;YAAApF,QAAA,eAEvC5F,OAAA,CAACnD,IAAI;cAACuK,SAAS;cAACC,OAAO,EAAE,CAAE;cAACrB,UAAU,EAAC,QAAQ;cAAAJ,QAAA,gBAC7C5F,OAAA,CAACnD,IAAI;gBAACyK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,eACvB5F,OAAA,CAACtD,GAAG;kBAACmJ,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAJ,QAAA,gBACjD5F,OAAA,CAACtD,GAAG;oBAACmJ,EAAE,EAAE;sBACPoD,KAAK,EAAE,CAAC;sBACRN,MAAM,EAAE,CAAC;sBACTvC,YAAY,EAAE,KAAK;sBACnBD,OAAO,EAAEwE,aAAa,CAACG,MAAM,CAACF,KAAK,CAAC;sBACpCzD,EAAE,EAAE;oBACN;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACL5G,OAAA,CAACtD,GAAG;oBAAAkJ,QAAA,gBACF5F,OAAA,CAACrD,UAAU;sBAAC2J,OAAO,EAAC,IAAI;sBAACT,EAAE,EAAE;wBAAEU,UAAU,EAAE,GAAG;wBAAEC,KAAK,EAAE;sBAAU,CAAE;sBAAAZ,QAAA,EAChEkF,MAAM,CAACK;oBAAM;sBAAA1E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACb5G,OAAA,CAACrD,UAAU;sBAAC2J,OAAO,EAAC,OAAO;sBAACT,EAAE,EAAE;wBAAEW,KAAK,EAAE;sBAAO,CAAE;sBAAAZ,QAAA,EAC/CkF,MAAM,CAACM;oBAAS;sBAAA3E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEP5G,OAAA,CAACnD,IAAI;gBAACyK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,gBACvB5F,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEP,EAAE,EAAE;kBAAI,CAAE;kBAAAL,QAAA,EAAC;gBAE5D;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE;kBAAI,CAAE;kBAAAX,QAAA,EACjDkF,MAAM,CAACO;gBAAU;kBAAA5E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEP5G,OAAA,CAACnD,IAAI;gBAACyK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,gBACvB5F,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEP,EAAE,EAAE;kBAAI,CAAE;kBAAAL,QAAA,EAAC;gBAE5D;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE;kBAAI,CAAE;kBAAAX,QAAA,GACjDkF,MAAM,CAAClD,YAAY,EAAC,GACvB;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEP5G,OAAA,CAACnD,IAAI;gBAACyK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,gBACvB5F,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEP,EAAE,EAAE;kBAAI,CAAE;kBAAAL,QAAA,EAAC;gBAE5D;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAZ,QAAA,GACnEkF,MAAM,CAACQ,gBAAgB,EAAC,GAC3B;gBAAA;kBAAA7E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEP5G,OAAA,CAACnD,IAAI;gBAACyK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,gBACvB5F,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEP,EAAE,EAAE;kBAAI,CAAE;kBAAAL,QAAA,EAAC;gBAE5D;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAZ,QAAA,GACnEkF,MAAM,CAACS,aAAa,EAAC,GACxB;gBAAA;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEP5G,OAAA,CAACnD,IAAI;gBAACyK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,eACvB5F,OAAA,CAACtD,GAAG;kBAACmJ,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE;kBAAS,CAAE;kBAAAH,QAAA,EACpD9D,cAAc,CAAC0I,GAAG,CAACM,MAAM,CAACE,EAAE,CAAC,gBAC5BhL,OAAA,CAACb,cAAc;oBAAC0G,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBACzC5G,OAAA,CAACf,cAAc;oBAAC4G,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAExC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAGL9E,cAAc,CAAC0I,GAAG,CAACM,MAAM,CAACE,EAAE,CAAC,iBAC5BhL,OAAA,CAACtD,GAAG;YAACmJ,EAAE,EAAE;cAAEK,CAAC,EAAE,CAAC;cAAEC,OAAO,EAAE;YAAU,CAAE;YAAAP,QAAA,gBACpC5F,OAAA,CAACrD,UAAU;cAAC2J,OAAO,EAAC,IAAI;cAACT,EAAE,EAAE;gBAAEI,EAAE,EAAE,CAAC;gBAAEM,UAAU,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAU,CAAE;cAAAZ,QAAA,GAAC,kBACzD,EAACkF,MAAM,CAACU,cAAc,CAAClI,MAAM,EAAC,GAChD;YAAA;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZkE,MAAM,CAACU,cAAc,CAAClI,MAAM,GAAG,CAAC,gBAC/BtD,OAAA,CAACnD,IAAI;cAACuK,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAzB,QAAA,EACxBkF,MAAM,CAACU,cAAc,CAAClC,GAAG,CAAEmC,IAAI,iBAC9BzL,OAAA,CAACnD,IAAI;gBAACyK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA7B,QAAA,eAC9B5F,OAAA,CAACpD,KAAK;kBAACiJ,EAAE,EAAE;oBAAEK,CAAC,EAAE,CAAC;oBAAEG,MAAM,EAAE,mBAAmB;oBAAEF,OAAO,EAAE;kBAAQ,CAAE;kBAAAP,QAAA,gBACjE5F,OAAA,CAACrD,UAAU;oBAAC2J,OAAO,EAAC,WAAW;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE,GAAG;sBAAEN,EAAE,EAAE;oBAAE,CAAE;oBAAAL,QAAA,EAC5D6F,IAAI,CAACC;kBAAY;oBAAAjF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACb5G,OAAA,CAACrD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE,MAAM;sBAAEP,EAAE,EAAE;oBAAI,CAAE;oBAAAL,QAAA,GAAC,oBACxC,eAAA5F,OAAA;sBAAA4F,QAAA,GAAS6F,IAAI,CAACH,gBAAgB,EAAC,GAAC;oBAAA;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACb5G,OAAA,CAACrD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO,CAAE;oBAAAZ,QAAA,GAAC,aACtC,EAAC,IAAIjC,IAAI,CAAC8H,IAAI,CAACE,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;kBAAA;oBAAAnF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC,GAX4B6E,IAAI,CAACT,EAAE;gBAAAvE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYvC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAEP5G,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEgD,SAAS,EAAE,QAAQ;gBAAE3C,CAAC,EAAE,CAAC;gBAAEC,OAAO,EAAE,OAAO;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAR,QAAA,eACxE5F,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN,eAGD5G,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEmD,EAAE,EAAE,CAAC;gBAAE9C,CAAC,EAAE,CAAC;gBAAEC,OAAO,EAAE,OAAO;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAoB,CAAE;cAAAT,QAAA,eACvF5F,OAAA,CAACnD,IAAI;gBAACuK,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAzB,QAAA,gBACzB5F,OAAA,CAACnD,IAAI;kBAACyK,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAA5B,QAAA,gBACvB5F,OAAA,CAACrD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE,MAAM;sBAAEP,EAAE,EAAE;oBAAI,CAAE;oBAAAL,QAAA,EAAC;kBAE5D;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE;oBAAI,CAAE;oBAAAX,QAAA,EACjDkF,MAAM,CAACe;kBAAS;oBAAApF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACP5G,OAAA,CAACnD,IAAI;kBAACyK,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAA5B,QAAA,gBACvB5F,OAAA,CAACrD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE,MAAM;sBAAEP,EAAE,EAAE;oBAAI,CAAE;oBAAAL,QAAA,EAAC;kBAE5D;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE;oBAAI,CAAE;oBAAAX,QAAA,EACjD,IAAIjC,IAAI,CAACmH,MAAM,CAACgB,WAAW,CAAC,CAACF,kBAAkB,CAAC,OAAO;kBAAC;oBAAAnF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACP5G,OAAA,CAACnD,IAAI;kBAACyK,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAA5B,QAAA,gBACvB5F,OAAA,CAACrD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE,MAAM;sBAAEP,EAAE,EAAE;oBAAI,CAAE;oBAAAL,QAAA,EAAC;kBAE5D;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5G,OAAA,CAACtD,GAAG;oBAACmJ,EAAE,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE;oBAAS,CAAE;oBAAAJ,QAAA,gBACjD5F,OAAA,CAACtD,GAAG;sBAACmJ,EAAE,EAAE;wBACPoD,KAAK,EAAE,CAAC;wBACRN,MAAM,EAAE,CAAC;wBACTvC,YAAY,EAAE,KAAK;wBACnBD,OAAO,EAAEwE,aAAa,CAACG,MAAM,CAACF,KAAK,CAAC;wBACpCzD,EAAE,EAAE;sBACN;oBAAE;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACL5G,OAAA,CAACrD,UAAU;sBAAC2J,OAAO,EAAC,OAAO;sBAACT,EAAE,EAAE;wBAAEU,UAAU,EAAE;sBAAI,CAAE;sBAAAX,QAAA,EACjDkF,MAAM,CAACF;oBAAK;sBAAAnE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA,GA1JSkE,MAAM,CAACE,EAAE;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2Jd,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5G,OAAA,CAACpD,KAAK;QAACiJ,EAAE,EAAE;UAAEK,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE,SAAS;UAAEE,MAAM,EAAE;QAAoB,CAAE;QAAAT,QAAA,gBACnE5F,OAAA,CAACrD,UAAU;UAAC2J,OAAO,EAAC,IAAI;UAACT,EAAE,EAAE;YAAEI,EAAE,EAAE,CAAC;YAAEM,UAAU,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAZ,QAAA,EAAC;QAE3E;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5G,OAAA,CAACnD,IAAI;UAACuK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,gBACzB5F,OAAA,CAACnD,IAAI;YAACyK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eACvB5F,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEgD,SAAS,EAAE;cAAS,CAAE;cAAAjD,QAAA,gBAC/B5F,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE,SAAS;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,EACvED,IAAI,CAACkF,MAAM,CAACvH;cAAM;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACb5G,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACP5G,OAAA,CAACnD,IAAI;YAACyK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eACvB5F,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEgD,SAAS,EAAE;cAAS,CAAE;cAAAjD,QAAA,gBAC/B5F,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE,SAAS;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,EACvED,IAAI,CAACkF,MAAM,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACT,cAAc,CAAClI,MAAM,EAAE,CAAC;cAAC;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACb5G,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACP5G,OAAA,CAACnD,IAAI;YAACyK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eACvB5F,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEgD,SAAS,EAAE;cAAS,CAAE;cAAAjD,QAAA,gBAC/B5F,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE,SAAS;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,GACvED,IAAI,CAACkF,MAAM,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACX,gBAAgB,EAAE,CAAC,CAAC,EAAC,GAC/D;cAAA;gBAAA7E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACP5G,OAAA,CAACnD,IAAI;YAACyK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eACvB5F,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEgD,SAAS,EAAE;cAAS,CAAE;cAAAjD,QAAA,gBAC/B5F,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE,SAAS;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,GACvED,IAAI,CAACkF,MAAM,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACV,aAAa,EAAE,CAAC,CAAC,EAAC,GAC5D;cAAA;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;gBAAC2J,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAMD,MAAMsF,uBAAuB,GAAIvG,IAAI,iBACnC3F,OAAA,CAACtD,GAAG;IAAAkJ,QAAA,gBAEF5F,OAAA,CAACtD,GAAG;MAACmJ,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzF5F,OAAA,CAACrD,UAAU;QAAC2J,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEU,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAZ,QAAA,EAAC;MAEzE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5G,OAAA,CAAC/B,gBAAgB;QACf4I,OAAO,eACL7G,OAAA,CAAChC,MAAM;UACL8I,OAAO,EAAElF,UAAW;UACpBmF,QAAQ,EAAGC,CAAC,IAAKnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDN,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDM,KAAK,eACHlH,OAAA,CAACtD,GAAG;UAACmJ,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,gBACjD5F,OAAA,CAACX,aAAa;YAACwG,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5G,OAAA,CAACnD,IAAI;MAACuK,SAAS;MAACC,OAAO,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxC5F,OAAA,CAACnD,IAAI;QAACyK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvB5F,OAAA,CAACpD,KAAK;UAACiJ,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE2C,SAAS,EAAE,QAAQ;YAAE1C,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAChF5F,OAAA,CAACrD,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,GACxDD,IAAI,CAACwG,oBAAoB,EAAC,GAC7B;UAAA;YAAA1F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;YAAC2J,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrD5G,OAAA,CAACrD,UAAU;YAAC2J,OAAO,EAAC,SAAS;YAAAV,QAAA,GAAED,IAAI,CAACxE,WAAW,EAAC,KAAG,EAACwE,IAAI,CAACvE,SAAS;UAAA;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACP5G,OAAA,CAACnD,IAAI;QAACyK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvB5F,OAAA,CAACpD,KAAK;UAACiJ,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE2C,SAAS,EAAE,QAAQ;YAAE1C,OAAO,EAAE,WAAW;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAC7E5F,OAAA,CAACrD,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,EACxDD,IAAI,CAACyG;UAAa;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACb5G,OAAA,CAACrD,UAAU;YAAC2J,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAa;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACP5G,OAAA,CAACnD,IAAI;QAACyK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvB5F,OAAA,CAACpD,KAAK;UAACiJ,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE2C,SAAS,EAAE,QAAQ;YAAE1C,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAChF5F,OAAA,CAACrD,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,GACxDD,IAAI,CAAC4C,iBAAiB,EAAC,GAC1B;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;YAAC2J,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACP5G,OAAA,CAACnD,IAAI;QAACyK,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvB5F,OAAA,CAACpD,KAAK;UAACiJ,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE2C,SAAS,EAAE,QAAQ;YAAE1C,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAChF5F,OAAA,CAACrD,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,GACxDpC,IAAI,CAACC,KAAK,CAACkC,IAAI,CAACwG,oBAAoB,GAAGxG,IAAI,CAACyG,aAAa,GAAG,CAAC,CAAC,EAAC,GAClE;UAAA;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;YAAC2J,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAe;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGNhF,UAAU,iBACT5B,OAAA,CAACtD,GAAG;MAACmJ,EAAE,EAAE;QAAEI,EAAE,EAAE,CAAC;QAAEgD,KAAK,EAAE;MAAO,CAAE;MAAArD,QAAA,eAChC5F,OAAA,CAACF,aAAa;QAAC6F,IAAI,EAAEA;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGD5G,OAAA,CAACpD,KAAK;MAACiJ,EAAE,EAAE;QAAEK,CAAC,EAAE;MAAE,CAAE;MAAAN,QAAA,gBAClB5F,OAAA,CAACrD,UAAU;QAAC2J,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEI,EAAE,EAAE,CAAC;UAAEM,UAAU,EAAE;QAAI,CAAE;QAAAX,QAAA,EAAC;MAEzD;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5G,OAAA,CAACP,eAAe;QACdkG,IAAI,EAAEA,IAAI,CAAC0G,gBAAgB,IAAI,EAAG;QAClCxC,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,MAAM;UAAEC,UAAU,EAAE,MAAM;UAAEd,KAAK,EAAE;QAAI,CAAC,EACjD;UAAEa,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,cAAc;UAAEd,KAAK,EAAE,GAAG;UAAEe,KAAK,EAAE,OAAO;UAAEsC,QAAQ,EAAE,QAAQ;UAC1FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAAC7C,KAAK;QAAI,CAAC,CACxC;QACFO,QAAQ,EAAE;MAAG;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAID,MAAM6F,YAAY,GAAGA,CAAA,kBACnBzM,OAAA,CAAC5C,MAAM;IAACkI,IAAI,EAAE5E,UAAW;IAACgM,OAAO,EAAEjH,iBAAkB;IAACkH,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAhH,QAAA,gBAC3E5F,OAAA,CAAC3C,WAAW;MAAAuI,QAAA,EACThF,UAAU,KAAK,cAAc,GAAG,yBAAyB,GAAG;IAAe;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eACd5G,OAAA,CAAC1C,aAAa;MAAAsI,QAAA,GACXpF,KAAK,iBACJR,OAAA,CAAC9C,KAAK;QAAC2P,QAAQ,EAAC,OAAO;QAAChH,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,EACnCpF;MAAK;QAAAiG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAED5G,OAAA,CAACnD,IAAI;QAACuK,SAAS;QAACC,OAAO,EAAE,CAAE;QAACxB,EAAE,EAAE;UAAEmD,EAAE,EAAE;QAAE,CAAE;QAAApD,QAAA,gBACxC5F,OAAA,CAACnD,IAAI;UAACyK,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA3B,QAAA,eAChB5F,OAAA,CAACxC,WAAW;YAACoP,SAAS;YAAAhH,QAAA,gBACpB5F,OAAA,CAACvC,UAAU;cAAAmI,QAAA,EAAC;YAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChC5G,OAAA,CAACtC,MAAM;cACLiK,KAAK,EAAE3G,QAAQ,CAACE,OAAQ;cACxBgG,KAAK,EAAC,SAAS;cACfH,QAAQ,EAAGC,CAAC,IAAK/F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAE8F,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cAAA/B,QAAA,gBAEvE5F,OAAA,CAACrC,QAAQ;gBAACgK,KAAK,EAAC,OAAO;gBAAA/B,QAAA,EAAC;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvD5G,OAAA,CAACrC,QAAQ;gBAACgK,KAAK,EAAC,KAAK;gBAAA/B,QAAA,EAAC;cAAY;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7C5G,OAAA,CAACrC,QAAQ;gBAACgK,KAAK,EAAC,OAAO;gBAAA/B,QAAA,EAAC;cAAc;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAINhG,UAAU,KAAK,cAAc,iBAC5BZ,OAAA,CAAAE,SAAA;UAAA0F,QAAA,gBACE5F,OAAA,CAACnD,IAAI;YAACyK,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACf5F,OAAA,CAACpC,SAAS;cACRgP,SAAS;cACTE,IAAI,EAAC,MAAM;cACX5F,KAAK,EAAC,aAAa;cACnBS,KAAK,EAAE3G,QAAQ,CAACG,WAAY;cAC5B4F,QAAQ,EAAGC,CAAC,IAAK/F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAE6F,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cAC3EoF,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP5G,OAAA,CAACnD,IAAI;YAACyK,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACf5F,OAAA,CAACpC,SAAS;cACRgP,SAAS;cACTE,IAAI,EAAC,MAAM;cACX5F,KAAK,EAAC,WAAW;cACjBS,KAAK,EAAE3G,QAAQ,CAACI,SAAU;cAC1B2F,QAAQ,EAAGC,CAAC,IAAK/F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAE4F,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cACzEoF,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChB5G,OAAA,CAACzC,aAAa;MAAAqI,QAAA,gBACZ5F,OAAA,CAAChD,MAAM;QAACkO,OAAO,EAAEzF,iBAAkB;QAAAG,QAAA,EAAC;MAAO;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpD5G,OAAA,CAAChD,MAAM;QACLkO,OAAO,EAAE1F,oBAAqB;QAC9Bc,OAAO,EAAC,WAAW;QACnB2G,QAAQ,EAAE3M,OAAQ;QAClB4M,SAAS,EAAE5M,OAAO,gBAAGN,OAAA,CAAC7C,gBAAgB;UAAC6K,IAAI,EAAE;QAAG;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG5G,OAAA,CAACrB,cAAc;UAAA8H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAhB,QAAA,EAExEtF,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACE5G,OAAA,CAACtD,GAAG;IAACyQ,SAAS,EAAC,sCAAsC;IAAAvH,QAAA,gBAEnD5F,OAAA,CAACtD,GAAG;MAACmJ,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACpF5F,OAAA,CAACT,eAAe;QAAAkH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGLtG,OAAO,iBACNN,OAAA,CAACtD,GAAG;MAACmJ,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEqH,EAAE,EAAE;MAAE,CAAE;MAAAxH,QAAA,eAC5D5F,OAAA,CAAC7C,gBAAgB;QAAAsJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGD5G,OAAA,CAACtD,GAAG;MAACmJ,EAAE,EAAE;QAAEmD,EAAE,EAAE;MAAE,CAAE;MAAApD,QAAA,gBAEjB5F,OAAA,CAACtD,GAAG;QAACmJ,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,gBACjB5F,OAAA,CAACrD,UAAU;UAAC2J,OAAO,EAAC,IAAI;UAACT,EAAE,EAAE;YAAEU,UAAU,EAAE,GAAG;YAAEC,KAAK,EAAE,SAAS;YAAEP,EAAE,EAAE,CAAC;YAAE4C,SAAS,EAAE;UAAS,CAAE;UAAAjD,QAAA,EAAC;QAEhG;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5G,OAAA,CAACnD,IAAI;UAACuK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,gBAEzB5F,OAAA,CAACnD,IAAI;YAACyK,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC7B5F,OAAA,CAAClD,IAAI;cACHqQ,SAAS,EAAE,eAAerM,kBAAkB,KAAK,UAAU,GAAG,sBAAsB,GAAG,EAAE,EAAG;cAC5F+E,EAAE,EAAE;gBACF8C,MAAM,EAAE,OAAO;gBACfoC,MAAM,EAAE,SAAS;gBACjB1E,MAAM,EAAEvF,kBAAkB,KAAK,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;gBACrFqF,OAAO,EAAErF,kBAAkB,KAAK,UAAU,GAAG,SAAS,GAAG,OAAO;gBAChEqI,UAAU,EAAE;cACd,CAAE;cACF+B,OAAO,EAAEA,CAAA,KAAMnK,qBAAqB,CAAC,UAAU,CAAE;cAAA6E,QAAA,eAEjD5F,OAAA,CAACjD,WAAW;gBAAC8I,EAAE,EAAE;kBAAEK,CAAC,EAAE,CAAC;kBAAE2C,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE7C,OAAO,EAAE,MAAM;kBAAEuH,aAAa,EAAE,QAAQ;kBAAEtH,cAAc,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjI5F,OAAA,CAAC7B,cAAc;kBAAC0H,EAAE,EAAE;oBAAE+C,QAAQ,EAAE,EAAE;oBAAEpC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjE5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,WAAW;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE2C,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEtF;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEoC,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEvE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGP5G,OAAA,CAACnD,IAAI;YAACyK,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC7B5F,OAAA,CAAClD,IAAI;cACH+I,EAAE,EAAE;gBACF8C,MAAM,EAAE,OAAO;gBACfoC,MAAM,EAAE,SAAS;gBACjB1E,MAAM,EAAEvF,kBAAkB,KAAK,KAAK,GAAG,mBAAmB,GAAG,mBAAmB;gBAChFqF,OAAO,EAAErF,kBAAkB,KAAK,KAAK,GAAG,SAAS,GAAG,OAAO;gBAC3DqI,UAAU,EAAE;cACd,CAAE;cACF+B,OAAO,EAAEA,CAAA,KAAMnK,qBAAqB,CAAC,KAAK,CAAE;cAAA6E,QAAA,eAE5C5F,OAAA,CAACjD,WAAW;gBAAC8I,EAAE,EAAE;kBAAEK,CAAC,EAAE,CAAC;kBAAE2C,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE7C,OAAO,EAAE,MAAM;kBAAEuH,aAAa,EAAE,QAAQ;kBAAEtH,cAAc,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjI5F,OAAA,CAACzB,QAAQ;kBAACsH,EAAE,EAAE;oBAAE+C,QAAQ,EAAE,EAAE;oBAAEpC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3D5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,WAAW;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE2C,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEtF;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEoC,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEvE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAKP5G,OAAA,CAACnD,IAAI;YAACyK,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC7B5F,OAAA,CAAClD,IAAI;cACH+I,EAAE,EAAE;gBACF8C,MAAM,EAAE,OAAO;gBACfoC,MAAM,EAAE,SAAS;gBACjB1E,MAAM,EAAEvF,kBAAkB,KAAK,gBAAgB,GAAG,mBAAmB,GAAG,mBAAmB;gBAC3FqF,OAAO,EAAErF,kBAAkB,KAAK,gBAAgB,GAAG,SAAS,GAAG,OAAO;gBACtEqI,UAAU,EAAE;cACd,CAAE;cACF+B,OAAO,EAAEA,CAAA,KAAMnK,qBAAqB,CAAC,gBAAgB,CAAE;cAAA6E,QAAA,eAEvD5F,OAAA,CAACjD,WAAW;gBAAC8I,EAAE,EAAE;kBAAEK,CAAC,EAAE,CAAC;kBAAE2C,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE7C,OAAO,EAAE,MAAM;kBAAEuH,aAAa,EAAE,QAAQ;kBAAEtH,cAAc,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjI5F,OAAA,CAAC3B,YAAY;kBAACwH,EAAE,EAAE;oBAAE+C,QAAQ,EAAE,EAAE;oBAAEpC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/D5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,WAAW;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE2C,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEtF;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5G,OAAA,CAACrD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEoC,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEvE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN5G,OAAA,CAACtD,GAAG;QAACmJ,EAAE,EAAE;UAAEyH,SAAS,EAAE,OAAO;UAAErE,KAAK,EAAE;QAAO,CAAE;QAAArD,QAAA,GAE5C9E,kBAAkB,KAAK,UAAU,iBAChCd,OAAA,CAACpD,KAAK;UAACiJ,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAEE,YAAY,EAAE;UAAE,CAAE;UAAAR,QAAA,EAClCtE,WAAW,CAACE,QAAQ,gBACnBxB,OAAA,CAACtD,GAAG;YAAAkJ,QAAA,gBACF5F,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9D5F,OAAA,CAAChD,MAAM;gBACLkQ,SAAS,eAAElN,OAAA,CAACvB,YAAY;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsE,OAAO,EAAEA,CAAA,KAAMlG,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAE;gBAC3DsB,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5G,OAAA,CAAChD,MAAM;gBACLkQ,SAAS,eAAElN,OAAA,CAACvB,YAAY;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsE,OAAO,EAAEA,CAAA,KAAMlG,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAE;gBAC7DsB,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLlB,oBAAoB,CAACpE,WAAW,CAACE,QAAQ,CAAC;UAAA;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GACJtG,OAAO,gBACTN,OAAA,CAACN,UAAU;YACToN,IAAI,EAAC,SAAS;YACd7H,UAAU,EAAC,UAAU;YACrByC,KAAK,EAAC,mCAAmC;YACzC6F,WAAW,EAAC;UAAsD;YAAA9G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,gBAEF5G,OAAA,CAACN,UAAU;YACToN,IAAI,EAAC,OAAO;YACZ7H,UAAU,EAAC,UAAU;YACrByC,KAAK,EAAC,wBAAwB;YAC9B6F,WAAW,EAAC,mFAAmF;YAC/FC,OAAO,EAAEA,CAAA,KAAM;cACbjN,UAAU,CAAC,IAAI,CAAC;cAChBf,aAAa,CAAC4C,iBAAiB,CAAC/B,UAAU,EAAE,OAAO,CAAC,CACjDoN,IAAI,CAAC9H,IAAI,IAAI;gBACZpE,cAAc,CAACuD,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACPtD,QAAQ,EAAEmE,IAAI,CAACnD;gBACjB,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAAC/B,KAAK,CAAC,iCAAiC,EAAE8B,GAAG,CAAC;cACvD,CAAC,CAAC,CACDoL,OAAO,CAAC,MAAM;gBACbnN,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA9F,kBAAkB,KAAK,KAAK,iBAC3Bd,OAAA,CAACpD,KAAK;UAACiJ,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAEE,YAAY,EAAE;UAAE,CAAE;UAAAR,QAAA,EAClCtE,WAAW,CAACG,GAAG,gBACdzB,OAAA,CAACtD,GAAG;YAAAkJ,QAAA,gBACF5F,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9D5F,OAAA,CAAChD,MAAM;gBACLkQ,SAAS,eAAElN,OAAA,CAACvB,YAAY;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsE,OAAO,EAAEA,CAAA,KAAMlG,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAE;gBACtDsB,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5G,OAAA,CAAChD,MAAM;gBACLkQ,SAAS,eAAElN,OAAA,CAACvB,YAAY;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsE,OAAO,EAAEA,CAAA,KAAMlG,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAE;gBACxDsB,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLuD,eAAe,CAAC7I,WAAW,CAACG,GAAG,CAAC;UAAA;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,GACJtG,OAAO,gBACTN,OAAA,CAACN,UAAU;YACToN,IAAI,EAAC,SAAS;YACd7H,UAAU,EAAC,KAAK;YAChByC,KAAK,EAAC,mCAAmC;YACzC6F,WAAW,EAAC;UAAyC;YAAA9G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,gBAEF5G,OAAA,CAACN,UAAU;YACToN,IAAI,EAAC,OAAO;YACZ7H,UAAU,EAAC,KAAK;YAChByC,KAAK,EAAC,wBAAwB;YAC9B6F,WAAW,EAAC,gFAAgF;YAC5FC,OAAO,EAAEA,CAAA,KAAM;cACbjN,UAAU,CAAC,IAAI,CAAC;cAChBf,aAAa,CAACkD,mBAAmB,CAACrC,UAAU,EAAE,OAAO,CAAC,CACnDoN,IAAI,CAAC9H,IAAI,IAAI;gBACZpE,cAAc,CAACuD,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACPrD,GAAG,EAAEkE,IAAI,CAACnD;gBACZ,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAE8B,GAAG,CAAC;cAClD,CAAC,CAAC,CACDoL,OAAO,CAAC,MAAM;gBACbnN,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA9F,kBAAkB,KAAK,gBAAgB,iBACtCd,OAAA,CAACpD,KAAK;UAACiJ,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAEE,YAAY,EAAE;UAAE,CAAE;UAAAR,QAAA,EAClCtE,WAAW,CAACK,aAAa,gBACxB3B,OAAA,CAACtD,GAAG;YAAAkJ,QAAA,gBACF5F,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9D5F,OAAA,CAAChD,MAAM;gBACLkQ,SAAS,eAAElN,OAAA,CAACvB,YAAY;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsE,OAAO,EAAEA,CAAA,KAAMlG,wBAAwB,CAAC,gBAAgB,EAAE,KAAK,CAAE;gBACjEsB,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5G,OAAA,CAAChD,MAAM;gBACLkQ,SAAS,eAAElN,OAAA,CAACvB,YAAY;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsE,OAAO,EAAEA,CAAA,KAAMlG,wBAAwB,CAAC,gBAAgB,EAAE,OAAO,CAAE;gBACnEsB,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLwD,yBAAyB,CAAC9I,WAAW,CAACK,aAAa,CAAC;UAAA;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,GACJtG,OAAO,gBACTN,OAAA,CAACN,UAAU;YACToN,IAAI,EAAC,SAAS;YACd7H,UAAU,EAAC,gBAAgB;YAC3ByC,KAAK,EAAC,+BAA+B;YACrC6F,WAAW,EAAC;UAA4D;YAAA9G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,gBAEF5G,OAAA,CAACN,UAAU;YACToN,IAAI,EAAC,OAAO;YACZ7H,UAAU,EAAC,gBAAgB;YAC3ByC,KAAK,EAAC,wBAAwB;YAC9B6F,WAAW,EAAC,4EAA4E;YACxFC,OAAO,EAAEA,CAAA,KAAMnJ,iBAAiB,CAAC,CAAE;YACnC/D,OAAO,EAAEA;UAAQ;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA9F,kBAAkB,KAAK,cAAc,iBACpCd,OAAA,CAACpD,KAAK;UAACiJ,EAAE,EAAE;YAAEK,CAAC,EAAE;UAAE,CAAE;UAAAN,QAAA,EACjBtE,WAAW,CAACqM,WAAW,gBACtB3N,OAAA,CAACtD,GAAG;YAAAkJ,QAAA,gBACF5F,OAAA,CAACtD,GAAG;cAACmJ,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9D5F,OAAA,CAAChD,MAAM;gBACLkQ,SAAS,eAAElN,OAAA,CAACvB,YAAY;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsE,OAAO,EAAEA,CAAA,KAAMlG,wBAAwB,CAAC,cAAc,EAAE,KAAK,CAAE;gBAC/DsB,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5G,OAAA,CAAChD,MAAM;gBACLkQ,SAAS,eAAElN,OAAA,CAACvB,YAAY;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsE,OAAO,EAAEA,CAAA,KAAMlG,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAE;gBACjEsB,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLsF,uBAAuB,CAAC5K,WAAW,CAACqM,WAAW,CAAC;UAAA;YAAAlH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,gBAEN5G,OAAA,CAACN,UAAU;YACToN,IAAI,EAAC,iBAAiB;YACtB7H,UAAU,EAAC,cAAc;YACzByC,KAAK,EAAC,sBAAsB;YAC5B6F,WAAW,EAAC,8GAA2G;YACvHK,WAAW,EAAC,mBAAmB;YAC/BC,QAAQ,EAAEA,CAAA,KAAM;cACdhN,aAAa,CAAC,cAAc,CAAC;cAC7B;cACA,MAAMiN,KAAK,GAAG,IAAInK,IAAI,CAAC,CAAC;cACxB,MAAMoK,SAAS,GAAG,IAAIpK,IAAI,CAAC,CAAC;cAC5BoK,SAAS,CAACC,QAAQ,CAACF,KAAK,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;cAExChN,WAAW,CAAC;gBACV,GAAGD,QAAQ;gBACXG,WAAW,EAAE4M,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAClD/M,SAAS,EAAE0M,KAAK,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC;cACFxN,aAAa,CAAC,IAAI,CAAC;YACrB;UAAE;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL6F,YAAY,CAAC,CAAC;EAAA;IAAAhG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAACxG,EAAA,CApzCID,iBAAiB;EAAA,QACEb,SAAS;AAAA;AAAA8O,EAAA,GAD5BjO,iBAAiB;AAszCvB,eAAeA,iBAAiB;AAAC,IAAAiO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}