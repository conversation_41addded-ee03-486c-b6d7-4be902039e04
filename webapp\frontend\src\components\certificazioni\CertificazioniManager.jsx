import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Fab,
  Tooltip,
  InputAdornment,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
  Refresh as RefreshIcon,
  PictureAsPdf as PdfIcon
} from '@mui/icons-material';

import CertificazioniList from './CertificazioniList';
import CertificazioneForm from './CertificazioneForm';
import certificazioneService from '../../services/certificazioneService';

function CertificazioniManager({ cantiereId }) {
  const [certificazioni, setCertificazioni] = useState([]);
  const [strumenti, setStrumenti] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);
  
  // Dialog states
  const [showForm, setShowForm] = useState(false);
  const [selectedCertificazione, setSelectedCertificazione] = useState(null);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);

  // Statistics
  const [stats, setStats] = useState({
    total: 0,
    conformi: 0,
    nonConformi: 0,
    pending: 0
  });

  useEffect(() => {
    loadData();
  }, [cantiereId]);

  useEffect(() => {
    filterCertificazioni();
  }, [certificazioni, searchTerm]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [certData, strumentiData] = await Promise.all([
        certificazioneService.getCertificazioni(cantiereId),
        certificazioneService.getStrumenti(cantiereId)
      ]);
      
      setCertificazioni(certData);
      setStrumenti(strumentiData);
      calculateStats(certData);
    } catch (error) {
      setError('Errore nel caricamento dei dati: ' + (error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (data) => {
    const stats = {
      total: data.length,
      conformi: data.filter(cert => parseFloat(cert.valore_isolamento) >= 500).length,
      nonConformi: data.filter(cert => parseFloat(cert.valore_isolamento) < 500).length,
      pending: data.filter(cert => !cert.valore_isolamento).length
    };
    setStats(stats);
  };

  const filterCertificazioni = () => {
    if (!searchTerm.trim()) {
      setFilteredCertificazioni(certificazioni);
      return;
    }

    const filtered = certificazioni.filter(cert => 
      cert.id_cavo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cert.numero_certificato?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cert.id_operatore?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cert.cavo_tipologia?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredCertificazioni(filtered);
  };

  const handleSuccess = (message) => {
    setSuccess(message);
    setTimeout(() => setSuccess(''), 5000);
  };

  const handleError = (message) => {
    setError(message);
    setTimeout(() => setError(''), 5000);
  };

  const handleNewCertificazione = () => {
    setSelectedCertificazione(null);
    setShowForm(true);
  };

  const handleEditCertificazione = (certificazione) => {
    setSelectedCertificazione(certificazione);
    setShowForm(true);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setSelectedCertificazione(null);
    loadData();
    handleSuccess('Certificazione salvata con successo');
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setSelectedCertificazione(null);
  };

  const handleDeleteSuccess = () => {
    loadData();
    handleSuccess('Certificazione eliminata con successo');
  };

  const handleExport = async () => {
    try {
      setLoading(true);
      const blob = await certificazioneService.exportCertificazioni(cantiereId, { search: searchTerm });
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `certificazioni_cantiere_${cantiereId}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      handleSuccess('Export completato con successo');
    } catch (error) {
      handleError('Errore nell\'export: ' + (error.message || 'Errore sconosciuto'));
    } finally {
      setLoading(false);
    }
  };

  const renderStats = () => (
    <Grid container spacing={2} sx={{ mb: 3 }}>
      <Grid item xs={12} sm={6} md={3}>
        <Paper sx={{ p: 2, textAlign: 'center', borderRadius: 2 }}>
          <Typography variant="h4" sx={{ color: '#2196f3', fontWeight: 'bold' }}>
            {stats.total}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Totale Certificazioni
          </Typography>
        </Paper>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Paper sx={{ p: 2, textAlign: 'center', borderRadius: 2 }}>
          <Typography variant="h4" sx={{ color: '#4caf50', fontWeight: 'bold' }}>
            {stats.conformi}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Conformi (≥500 MΩ)
          </Typography>
        </Paper>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Paper sx={{ p: 2, textAlign: 'center', borderRadius: 2 }}>
          <Typography variant="h4" sx={{ color: '#f44336', fontWeight: 'bold' }}>
            {stats.nonConformi}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Non Conformi (&lt;500 MΩ)
          </Typography>
        </Paper>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Paper sx={{ p: 2, textAlign: 'center', borderRadius: 2 }}>
          <Typography variant="h4" sx={{ color: '#ff9800', fontWeight: 'bold' }}>
            {stats.pending}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            In Attesa
          </Typography>
        </Paper>
      </Grid>
    </Grid>
  );

  const renderToolbar = () => (
    <Paper sx={{ p: 2, mb: 3, borderRadius: 2 }}>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            placeholder="Cerca per ID cavo, certificato, operatore..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                '&:hover fieldset': {
                  borderColor: '#2196f3',
                },
              },
            }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadData}
              disabled={loading}
            >
              Aggiorna
            </Button>
            <Button
              variant="outlined"
              startIcon={<ExportIcon />}
              onClick={handleExport}
              disabled={loading}
            >
              Esporta
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleNewCertificazione}
              sx={{
                backgroundColor: '#2196f3',
                '&:hover': {
                  backgroundColor: '#1976d2'
                }
              }}
            >
              Nuova Certificazione
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ color: '#2196f3', fontWeight: 'bold' }}>
        Gestione Certificazioni Cavi
      </Typography>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 2, borderRadius: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mb: 2, borderRadius: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Statistics */}
      {renderStats()}

      {/* Toolbar */}
      {renderToolbar()}

      {/* Loading */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Certificazioni List */}
      {!loading && (
        <CertificazioniList
          certificazioni={filteredCertificazioni}
          onEdit={handleEditCertificazione}
          onDelete={handleDeleteSuccess}
          onSuccess={handleSuccess}
          onError={handleError}
          cantiereId={cantiereId}
        />
      )}

      {/* Form Dialog */}
      <Dialog
        open={showForm}
        onClose={handleFormCancel}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 2 }
        }}
      >
        <DialogContent sx={{ p: 0 }}>
          <CertificazioneForm
            cantiereId={cantiereId}
            certificazione={selectedCertificazione}
            strumenti={strumenti}
            onSuccess={handleFormSuccess}
            onCancel={handleFormCancel}
          />
        </DialogContent>
      </Dialog>
    </Box>
  );
}

export default CertificazioniManager;
